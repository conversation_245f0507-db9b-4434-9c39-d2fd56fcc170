#!/usr/bin/env python3
"""
安装脚本 - 自动安装依赖和配置环境
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return False
    print(f"✅ Python版本: {sys.version.split()[0]}")
    return True

def install_pip_dependencies():
    """安装pip依赖"""
    print("📦 安装Python依赖...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ])
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def setup_environment():
    """设置环境配置"""
    print("⚙️  配置环境...")
    
    # 复制.env文件
    if not Path(".env").exists():
        if Path(".env.example").exists():
            shutil.copy(".env.example", ".env")
            print("✅ 已创建.env配置文件")
        else:
            print("❌ 未找到.env.example文件")
            return False
    else:
        print("✅ .env文件已存在")
    
    # 创建必要目录
    directories = [
        "temp",
        "knowledge_base",
        "translation_history",
        "chroma_db"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录结构创建完成")
    return True

def check_chrome():
    """检查Chrome浏览器"""
    print("🌐 检查Chrome浏览器...")
    
    # 检查Chrome是否安装
    chrome_paths = [
        "/usr/bin/google-chrome",
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
    ]
    
    chrome_found = False
    for path in chrome_paths:
        if os.path.exists(path):
            chrome_found = True
            break
    
    if chrome_found:
        print("✅ Chrome浏览器已安装")
        
        # 测试WebDriver
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.quit()
            print("✅ Chrome WebDriver可用")
            return True
        except Exception as e:
            print(f"⚠️  Chrome WebDriver不可用: {e}")
            print("   网页爬虫功能将受限")
            return False
    else:
        print("⚠️  未检测到Chrome浏览器")
        print("   建议安装Chrome以启用网页爬虫功能")
        return False

def configure_api_key():
    """配置API密钥"""
    print("🔑 配置API密钥...")
    
    # 检查是否已配置
    try:
        from dotenv import load_dotenv
        load_dotenv()
        api_key = os.getenv("OPENROUTER_API_KEY")
        
        if api_key and api_key != "your_openrouter_api_key_here":
            print("✅ API密钥已配置")
            return True
    except:
        pass
    
    print("\n🔧 需要配置OpenRouter API密钥")
    print("1. 访问 https://openrouter.ai/ 注册账号")
    print("2. 获取API密钥")
    print("3. 编辑.env文件，设置OPENROUTER_API_KEY")
    print("\n或者现在输入API密钥:")
    
    api_key = input("请输入OpenRouter API密钥 (回车跳过): ").strip()
    
    if api_key:
        # 更新.env文件
        try:
            with open(".env", "r") as f:
                content = f.read()
            
            content = content.replace(
                "OPENROUTER_API_KEY=your_openrouter_api_key_here",
                f"OPENROUTER_API_KEY={api_key}"
            )
            
            with open(".env", "w") as f:
                f.write(content)
            
            print("✅ API密钥已保存")
            return True
        except Exception as e:
            print(f"❌ 保存API密钥失败: {e}")
            return False
    else:
        print("⚠️  请稍后手动配置API密钥")
        return False

def run_tests():
    """运行基本测试"""
    print("🧪 运行基本测试...")
    
    try:
        # 测试导入主要模块
        from config import Config
        from utils.document_processor import DocumentProcessor
        from services.translation_service import TranslationService
        
        print("✅ 模块导入测试通过")
        return True
    except Exception as e:
        print(f"❌ 模块测试失败: {e}")
        return False

def main():
    """主安装函数"""
    print("🚀 港交所上市公司披露报告翻译系统 - 安装向导")
    print("=" * 60)
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装Python依赖", install_pip_dependencies),
        ("设置环境配置", setup_environment),
        ("检查Chrome浏览器", check_chrome),
        ("配置API密钥", configure_api_key),
        ("运行基本测试", run_tests)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            if step_func():
                success_count += 1
            else:
                print(f"⚠️  {step_name}未完全成功")
        except Exception as e:
            print(f"❌ {step_name}失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 安装结果: {success_count}/{len(steps)} 步骤成功")
    
    if success_count >= len(steps) - 1:  # 允许一个步骤失败
        print("\n🎉 安装基本完成！")
        print("\n📝 下一步:")
        print("1. 确保.env文件中的OPENROUTER_API_KEY已正确设置")
        print("2. 运行测试: python test_system.py")
        print("3. 启动系统: python start.py")
        
        # 询问是否立即启动
        if input("\n是否现在启动系统? (y/N): ").lower() == 'y':
            print("\n🚀 启动系统...")
            try:
                from start import main as start_main
                start_main()
            except Exception as e:
                print(f"❌ 启动失败: {e}")
                print("请手动运行: python start.py")
    else:
        print("\n❌ 安装未完成，请检查错误信息并重试")
        print("或参考README.md进行手动安装")

if __name__ == "__main__":
    main()
