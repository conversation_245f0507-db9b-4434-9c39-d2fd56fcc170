"""
校对智能体 - 负责翻译质量校对
"""
import os
import json
import time
from typing import Dict, List, Optional, Tuple
import openai
import logging
from config import Config, MODEL_PRICING

# 确保agents目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)

class ReviewAgent:
    """校对智能体"""
    
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.OPENROUTER_API_KEY,
            base_url=Config.OPENROUTER_BASE_URL
        )
        self.model = Config.DEFAULT_REVIEW_MODEL
        
    def review_translation(self, translation_result: Dict, reference_translations: List[Dict],
                         company_info: Dict, translation_direction: str) -> Dict:
        """
        校对翻译结果
        
        Args:
            translation_result: 翻译结果
            reference_translations: 参考翻译
            company_info: 公司信息
            translation_direction: 翻译方向
            
        Returns:
            校对结果
        """
        try:
            # 构建校对提示
            prompt = self._build_review_prompt(
                translation_result, reference_translations, company_info, translation_direction
            )
            
            logger.info(f"开始校对第 {translation_result.get('page_number', '?')} 页")
            
            start_time = time.time()
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt(translation_direction)},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=2000
            )
            
            # 解析校对结果
            review_result = self._parse_review_response(response.choices[0].message.content)
            
            # 构建结果
            result = {
                'page_number': translation_result.get('page_number'),
                'review_score': review_result.get('score', 0),
                'consistency_score': review_result.get('consistency_score', 0),
                'accuracy_score': review_result.get('accuracy_score', 0),
                'terminology_score': review_result.get('terminology_score', 0),
                'issues_found': review_result.get('issues', []),
                'suggestions': review_result.get('suggestions', []),
                'reference_usage': review_result.get('reference_usage', ''),
                'needs_revision': review_result.get('needs_revision', False),
                'feedback': review_result.get('feedback', ''),
                'processing_time': time.time() - start_time,
                'model_used': self.model
            }
            
            # 判断是否需要修订
            if result['review_score'] < 7 or result['consistency_score'] < 6:
                result['needs_revision'] = True
                logger.warning(f"第 {translation_result.get('page_number', '?')} 页需要修订，评分: {result['review_score']}")
            else:
                logger.info(f"第 {translation_result.get('page_number', '?')} 页校对通过，评分: {result['review_score']}")
            
            return result
            
        except Exception as e:
            logger.error(f"校对失败: {e}")
            return {
                'page_number': translation_result.get('page_number'),
                'review_score': 0,
                'needs_revision': True,
                'error': str(e),
                'feedback': f"校对过程出错: {str(e)}"
            }
    
    def _build_review_prompt(self, translation_result: Dict, reference_translations: List[Dict],
                           company_info: Dict, translation_direction: str) -> str:
        """构建校对提示"""
        
        source_lang = "中文" if translation_direction == "zh_to_en" else "英文"
        target_lang = "英文" if translation_direction == "zh_to_en" else "中文"
        
        prompt = f"""请校对以下{source_lang}到{target_lang}的翻译质量。

公司信息：
- 公司名称（中文）: {company_info.get('company_name_zh', '未知')}
- 公司名称（英文）: {company_info.get('company_name_en', '未知')}
- 股票代码: {company_info.get('stock_code', '未知')}
- 报告类型: {company_info.get('report_type', '未知')}

原文：
{translation_result.get('original_content', '')}

译文：
{translation_result.get('translated_content', '')}

历史参考翻译（用于检查一致性）：
"""
        
        # 添加参考翻译
        for i, ref in enumerate(reference_translations[:3], 1):
            similarity = ref.get('similarity_score', 0)
            prompt += f"\n参考 {i} (相似度: {similarity:.2f}):\n"
            prompt += f"{ref['content'][:200]}...\n"
        
        prompt += f"""

校对要求：
1. 准确性评估：检查翻译是否准确传达原文意思，特别是数字、日期、金额等关键信息
2. 一致性评估：检查术语翻译是否与历史参考翻译保持一致
3. 专业性评估：检查是否使用了恰当的财务和法律术语
4. 格式评估：检查是否保持了原文的结构和格式
5. 流畅性评估：检查译文是否自然流畅

请以JSON格式返回校对结果：
{{
    "score": 整体评分(1-10),
    "accuracy_score": 准确性评分(1-10),
    "consistency_score": 一致性评分(1-10),
    "terminology_score": 术语专业性评分(1-10),
    "issues": ["发现的问题1", "发现的问题2"],
    "suggestions": ["改进建议1", "改进建议2"],
    "reference_usage": "是否充分利用了历史参考翻译",
    "needs_revision": 是否需要修订(true/false),
    "feedback": "详细的校对反馈和修改建议"
}}
"""
        
        return prompt
    
    def _get_system_prompt(self, translation_direction: str) -> str:
        """获取系统提示"""
        return """你是一位资深的翻译质量评估专家，专门评估香港上市公司披露报告的翻译质量。你具备以下专业能力：

1. 深厚的财务和法律知识背景
2. 丰富的中英文翻译经验
3. 敏锐的术语一致性判断能力
4. 严格的质量控制标准

评估原则：
- 准确性：翻译必须准确传达原文意思，不能有遗漏或错误
- 一致性：术语翻译必须与历史翻译保持一致，特别是公司特有术语
- 专业性：必须使用标准的财务和法律术语
- 完整性：不能遗漏重要信息
- 流畅性：译文应该自然流畅，符合目标语言习惯

评分标准：
- 9-10分：优秀，无明显问题
- 7-8分：良好，有轻微问题但可接受
- 5-6分：一般，有明显问题需要修订
- 1-4分：差，有严重问题必须重新翻译"""
    
    def _parse_review_response(self, response_text: str) -> Dict:
        """解析校对响应"""
        try:
            # 尝试解析JSON
            if response_text.strip().startswith('{'):
                return json.loads(response_text)
            
            # 如果不是JSON格式，尝试提取关键信息
            result = {
                'score': 5,
                'accuracy_score': 5,
                'consistency_score': 5,
                'terminology_score': 5,
                'issues': [],
                'suggestions': [],
                'reference_usage': '',
                'needs_revision': True,
                'feedback': response_text
            }
            
            lines = response_text.split('\n')
            for line in lines:
                line = line.strip()
                if '评分' in line or 'score' in line.lower():
                    # 提取数字
                    import re
                    numbers = re.findall(r'\d+', line)
                    if numbers:
                        score = int(numbers[0])
                        if 'accuracy' in line.lower() or '准确' in line:
                            result['accuracy_score'] = score
                        elif 'consistency' in line.lower() or '一致' in line:
                            result['consistency_score'] = score
                        elif 'terminology' in line.lower() or '术语' in line:
                            result['terminology_score'] = score
                        else:
                            result['score'] = score
                
                elif '问题' in line or 'issue' in line.lower():
                    result['issues'].append(line)
                elif '建议' in line or 'suggestion' in line.lower():
                    result['suggestions'].append(line)
            
            # 根据评分判断是否需要修订
            avg_score = (result['score'] + result['accuracy_score'] + 
                        result['consistency_score'] + result['terminology_score']) / 4
            result['needs_revision'] = avg_score < 7
            
            return result
            
        except Exception as e:
            logger.warning(f"解析校对响应失败: {e}")
            return {
                'score': 5,
                'accuracy_score': 5,
                'consistency_score': 5,
                'terminology_score': 5,
                'issues': ['解析响应失败'],
                'suggestions': ['请检查翻译质量'],
                'reference_usage': '未知',
                'needs_revision': True,
                'feedback': response_text
            }
    
    def generate_revision_feedback(self, review_result: Dict, reference_translations: List[Dict]) -> str:
        """
        生成修订反馈
        
        Args:
            review_result: 校对结果
            reference_translations: 参考翻译
            
        Returns:
            修订反馈文本
        """
        try:
            feedback_parts = []
            
            # 添加主要问题
            if review_result.get('issues'):
                feedback_parts.append("发现的主要问题：")
                for issue in review_result['issues']:
                    feedback_parts.append(f"- {issue}")
            
            # 添加改进建议
            if review_result.get('suggestions'):
                feedback_parts.append("\n改进建议：")
                for suggestion in review_result['suggestions']:
                    feedback_parts.append(f"- {suggestion}")
            
            # 添加参考翻译建议
            if reference_translations:
                feedback_parts.append("\n请特别参考以下历史翻译中的术语用法：")
                for i, ref in enumerate(reference_translations[:2], 1):
                    feedback_parts.append(f"参考 {i}: {ref['content'][:100]}...")
            
            # 添加具体的修订要求
            if review_result.get('consistency_score', 0) < 6:
                feedback_parts.append("\n特别注意：请确保术语翻译与历史翻译保持一致。")
            
            if review_result.get('accuracy_score', 0) < 6:
                feedback_parts.append("\n特别注意：请仔细检查数字、日期、金额等关键信息的准确性。")
            
            if review_result.get('terminology_score', 0) < 6:
                feedback_parts.append("\n特别注意：请使用更专业的财务和法律术语。")
            
            return "\n".join(feedback_parts)
            
        except Exception as e:
            logger.error(f"生成修订反馈失败: {e}")
            return review_result.get('feedback', '请根据校对结果进行修订。')
    
    def final_quality_check(self, all_translations: List[Dict]) -> Dict:
        """
        最终质量检查
        
        Args:
            all_translations: 所有翻译结果
            
        Returns:
            最终质量报告
        """
        try:
            total_pages = len(all_translations)
            passed_pages = sum(1 for t in all_translations if not t.get('needs_revision', True))
            
            # 计算平均分数
            scores = [t.get('review_score', 0) for t in all_translations if t.get('review_score')]
            avg_score = sum(scores) / len(scores) if scores else 0
            
            # 统计问题
            all_issues = []
            for t in all_translations:
                if t.get('issues_found'):
                    all_issues.extend(t['issues_found'])
            
            # 生成质量报告
            quality_report = {
                'total_pages': total_pages,
                'passed_pages': passed_pages,
                'pass_rate': passed_pages / total_pages if total_pages > 0 else 0,
                'average_score': avg_score,
                'common_issues': self._analyze_common_issues(all_issues),
                'overall_quality': self._determine_overall_quality(avg_score, passed_pages / total_pages if total_pages > 0 else 0),
                'recommendations': self._generate_final_recommendations(all_translations)
            }
            
            logger.info(f"最终质量检查完成：通过率 {quality_report['pass_rate']:.1%}，平均分 {avg_score:.1f}")
            return quality_report
            
        except Exception as e:
            logger.error(f"最终质量检查失败: {e}")
            return {
                'total_pages': len(all_translations),
                'passed_pages': 0,
                'pass_rate': 0,
                'average_score': 0,
                'error': str(e)
            }
    
    def _analyze_common_issues(self, all_issues: List[str]) -> List[Dict]:
        """分析常见问题"""
        try:
            issue_counts = {}
            for issue in all_issues:
                issue_counts[issue] = issue_counts.get(issue, 0) + 1
            
            # 按频率排序
            sorted_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)
            
            return [{'issue': issue, 'count': count} for issue, count in sorted_issues[:5]]
        except:
            return []
    
    def _determine_overall_quality(self, avg_score: float, pass_rate: float) -> str:
        """确定整体质量等级"""
        if avg_score >= 8 and pass_rate >= 0.9:
            return "优秀"
        elif avg_score >= 7 and pass_rate >= 0.8:
            return "良好"
        elif avg_score >= 6 and pass_rate >= 0.7:
            return "一般"
        else:
            return "需要改进"
    
    def _generate_final_recommendations(self, all_translations: List[Dict]) -> List[str]:
        """生成最终建议"""
        recommendations = []
        
        # 分析需要修订的页面
        revision_needed = [t for t in all_translations if t.get('needs_revision', True)]
        if revision_needed:
            recommendations.append(f"建议重点修订第 {', '.join(str(t.get('page_number', '?')) for t in revision_needed)} 页")
        
        # 分析一致性问题
        low_consistency = [t for t in all_translations if t.get('consistency_score', 0) < 6]
        if low_consistency:
            recommendations.append("建议加强术语一致性，参考历史翻译")
        
        # 分析准确性问题
        low_accuracy = [t for t in all_translations if t.get('accuracy_score', 0) < 6]
        if low_accuracy:
            recommendations.append("建议仔细检查数字、日期等关键信息的准确性")
        
        return recommendations
