"""
翻译智能体 - 负责内容翻译
"""

import os
import json
import time
from typing import Dict, List, Optional, Tuple
import openai
import tiktoken
import logging
from config import Config, MODEL_PRICING
from utils.json_parser import parse_translation_response

# 确保agents目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)


class TranslationAgent:
    """翻译智能体"""

    def __init__(self):
        self.client = openai.OpenAI(
            api_key=Config.OPENROUTER_API_KEY, base_url=Config.OPENROUTER_BASE_URL
        )
        self.model = Config.DEFAULT_TRANSLATION_MODEL
        self.encoding = tiktoken.get_encoding("cl100k_base")

    def translate_page(
        self,
        page_content: Dict,
        reference_translations: List[Dict],
        translation_direction: str,
        company_info: Dict,
    ) -> Dict:
        """
        翻译页面内容

        Args:
            page_content: 页面内容
            reference_translations: 参考翻译
            translation_direction: 翻译方向 (zh_to_en 或 en_to_zh)
            company_info: 公司信息

        Returns:
            翻译结果
        """
        try:
            # 构建翻译提示
            prompt = self._build_translation_prompt(
                page_content,
                reference_translations,
                translation_direction,
                company_info,
            )

            # 估算成本
            input_tokens = len(self.encoding.encode(prompt))
            estimated_cost = self._estimate_cost(input_tokens)

            logger.info(
                f"开始翻译第 {page_content.get('page_number', '?')} 页，预估成本: ${estimated_cost:.4f}"
            )

            # 调用API
            start_time = time.time()
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt(translation_direction),
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.1,
                max_tokens=4000,
            )

            # 解析响应
            translation_result = parse_translation_response(
                response.choices[0].message.content
            )

            # 计算实际成本
            actual_cost = self._calculate_actual_cost(response.usage)

            # 构建结果
            result = {
                "page_number": page_content.get("page_number"),
                "original_content": page_content.get("content", ""),
                "translated_content": translation_result.get("translated_text", ""),
                "translation_notes": translation_result.get("notes", ""),
                "reference_count": len(reference_translations),
                "processing_time": time.time() - start_time,
                "cost": actual_cost,
                "model_used": self.model,
                "success": True,
            }

            # 更新段落翻译
            if "paragraphs" in page_content:
                result["translated_paragraphs"] = self._translate_paragraphs(
                    page_content["paragraphs"],
                    translation_result.get("translated_text", ""),
                )

            logger.info(
                f"第 {page_content.get('page_number', '?')} 页翻译完成，实际成本: ${actual_cost:.4f}"
            )
            return result

        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return {
                "page_number": page_content.get("page_number"),
                "original_content": page_content.get("content", ""),
                "translated_content": "",
                "error": str(e),
                "success": False,
            }

    def _build_translation_prompt(
        self,
        page_content: Dict,
        reference_translations: List[Dict],
        translation_direction: str,
        company_info: Dict,
    ) -> str:
        """构建翻译提示"""

        source_lang = "中文" if translation_direction == "zh_to_en" else "英文"
        target_lang = "英文" if translation_direction == "zh_to_en" else "中文"

        prompt = f"""请将以下{source_lang}内容翻译成{target_lang}。

公司信息：
- 公司名称（中文）: {company_info.get('company_name_zh', '未知')}
- 公司名称（英文）: {company_info.get('company_name_en', '未知')}
- 股票代码: {company_info.get('stock_code', '未知')}
- 报告类型: {company_info.get('report_type', '未知')}

翻译要求：
1. 保持专业术语的一致性，特别是财务和法律术语
2. 尽可能参考以下历史翻译，保持译法一致
3. 保持原文的格式和结构
4. 确保数字、日期、金额等信息准确无误
5. 对于公司特有的术语，优先使用历史翻译中的译法

历史参考翻译（请优先参考这些译法）：
"""

        # 添加参考翻译
        for i, ref in enumerate(reference_translations[:5], 1):  # 最多5个参考
            similarity = ref.get("similarity_score", 0)
            prompt += f"\n参考 {i} (相似度: {similarity:.2f}):\n"
            if translation_direction == "zh_to_en":
                prompt += f"原文: {ref['content'][:200]}...\n"
                # 这里应该有对应的英文翻译，暂时用占位符
                prompt += f"译文: [对应英文翻译]\n"
            else:
                prompt += f"原文: {ref['content'][:200]}...\n"
                prompt += f"译文: [对应中文翻译]\n"

        prompt += f"""

待翻译内容：
{page_content.get('content', '')}

请严格按照以下JSON格式返回翻译结果，不要添加任何额外的文本或格式：

{
    "translated_text": "翻译后的完整文本",
    "notes": "翻译说明和注意事项"
}
"""

        return prompt

    def _get_system_prompt(self, translation_direction: str) -> str:
        """获取系统提示"""
        if translation_direction == "zh_to_en":
            return """你是一位专业的中英文翻译专家，专门翻译香港上市公司的披露报告。你具备深厚的财务、法律和商业知识，能够准确翻译各种专业术语。

翻译原则：
1. 准确性：确保翻译内容准确无误，特别是数字、日期、金额等关键信息
2. 一致性：保持术语翻译的一致性，特别是公司特有的术语
3. 专业性：使用标准的财务和法律英语表达
4. 格式保持：保持原文的段落结构和格式
5. 参考历史：优先使用历史翻译中已确立的译法"""
        else:
            return """你是一位专业的英中文翻译专家，专门翻译香港上市公司的披露报告。你具备深厚的财务、法律和商业知识，能够准确翻译各种专业术语。

翻译原则：
1. 准确性：确保翻译内容准确无误，特别是数字、日期、金额等关键信息
2. 一致性：保持术语翻译的一致性，特别是公司特有的术语
3. 专业性：使用标准的财务和法律中文表达
4. 格式保持：保持原文的段落结构和格式
5. 参考历史：优先使用历史翻译中已确立的译法"""

    def _clean_response_text(self, text: str) -> str:
        """清理响应文本中的控制字符"""
        import re

        # 移除控制字符，但保留换行符和制表符
        cleaned = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", text)
        return cleaned

    def _parse_translation_response(self, response_text: str) -> Dict:
        """解析翻译响应"""
        try:
            # 清理控制字符
            cleaned_text = self._clean_response_text(response_text)

            # 尝试解析JSON
            if cleaned_text.strip().startswith("{"):
                try:
                    return json.loads(cleaned_text)
                except json.JSONDecodeError as je:
                    logger.warning(f"JSON解析失败: {je}")
                    # 尝试提取JSON部分
                    try:
                        # 查找第一个完整的JSON对象
                        start = cleaned_text.find("{")
                        if start != -1:
                            brace_count = 0
                            end = start
                            for i, char in enumerate(cleaned_text[start:], start):
                                if char == "{":
                                    brace_count += 1
                                elif char == "}":
                                    brace_count -= 1
                                    if brace_count == 0:
                                        end = i + 1
                                        break

                            if end > start:
                                json_part = cleaned_text[start:end]
                                return json.loads(json_part)
                    except:
                        pass

            # 如果不是JSON格式，尝试提取内容
            lines = cleaned_text.strip().split("\n")
            translated_text = ""
            notes = ""

            in_translation = False
            in_notes = False

            for line in lines:
                if '"translated_text"' in line or "翻译结果" in line:
                    in_translation = True
                    in_notes = False
                elif '"notes"' in line or "说明" in line:
                    in_translation = False
                    in_notes = True
                elif in_translation:
                    translated_text += line + "\n"
                elif in_notes:
                    notes += line + "\n"

            return {"translated_text": translated_text.strip(), "notes": notes.strip()}

        except Exception as e:
            logger.warning(f"解析翻译响应失败: {e}")
            return {
                "translated_text": (
                    cleaned_text if "cleaned_text" in locals() else response_text
                ),
                "notes": "",
            }

    def _translate_paragraphs(
        self, paragraphs: List[Dict], full_translation: str
    ) -> List[Dict]:
        """将完整翻译分配到各个段落"""
        try:
            translated_paragraphs = []
            translation_lines = full_translation.split("\n")
            line_index = 0

            for para in paragraphs:
                original_text = para.get("text", "").strip()
                if not original_text:
                    translated_paragraphs.append({**para, "translated_text": ""})
                    continue

                # 简单的段落匹配逻辑
                translated_text = ""
                if line_index < len(translation_lines):
                    translated_text = translation_lines[line_index].strip()
                    line_index += 1

                translated_paragraphs.append(
                    {**para, "translated_text": translated_text}
                )

            return translated_paragraphs

        except Exception as e:
            logger.warning(f"段落翻译分配失败: {e}")
            return paragraphs

    def _estimate_cost(self, input_tokens: int, output_tokens: int = 1000) -> float:
        """估算翻译成本"""
        try:
            pricing = MODEL_PRICING.get(self.model, {"input": 0.01, "output": 0.03})
            input_cost = (input_tokens / 1000) * pricing["input"]
            output_cost = (output_tokens / 1000) * pricing["output"]
            return input_cost + output_cost
        except:
            return 0.05  # 默认估算

    def _calculate_actual_cost(self, usage) -> float:
        """计算实际成本"""
        try:
            pricing = MODEL_PRICING.get(self.model, {"input": 0.01, "output": 0.03})
            input_cost = (usage.prompt_tokens / 1000) * pricing["input"]
            output_cost = (usage.completion_tokens / 1000) * pricing["output"]
            return input_cost + output_cost
        except:
            return 0.0

    def revise_translation(
        self,
        original_translation: Dict,
        feedback: str,
        reference_translations: List[Dict],
    ) -> Dict:
        """
        根据反馈修订翻译

        Args:
            original_translation: 原始翻译结果
            feedback: 校对反馈
            reference_translations: 参考翻译

        Returns:
            修订后的翻译结果
        """
        try:
            prompt = f"""请根据以下反馈修订翻译：

原始翻译：
{original_translation.get('translated_content', '')}

校对反馈：
{feedback}

参考翻译（请参考这些译法）：
"""

            for i, ref in enumerate(reference_translations[:3], 1):
                prompt += f"\n参考 {i}: {ref['content'][:150]}...\n"

            prompt += "\n请提供修订后的翻译，以JSON格式返回：\n"
            prompt += (
                '{"revised_translation": "修订后的翻译", "revision_notes": "修订说明"}'
            )

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的翻译修订专家，根据反馈改进翻译质量。",
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.1,
                max_tokens=2000,
            )

            revision_result = parse_translation_response(
                response.choices[0].message.content
            )

            # 更新翻译结果
            revised_translation = original_translation.copy()
            revised_translation["translated_content"] = revision_result.get(
                "revised_translation",
                original_translation.get("translated_content", ""),
            )
            revised_translation["revision_notes"] = revision_result.get(
                "revision_notes", ""
            )
            revised_translation["revised"] = True

            return revised_translation

        except Exception as e:
            logger.error(f"翻译修订失败: {e}")
            return original_translation
