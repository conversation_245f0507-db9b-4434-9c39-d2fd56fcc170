"""
简化的参考知识库模块 - 不依赖外部模型
"""
import os
import json
import hashlib
from typing import List, Dict, Optional
import logging
import re
from collections import defaultdict

from utils.reference_processor import ReferenceProcessor

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)

class SimpleKnowledgeBase:
    """简化的参考知识库管理器"""
    
    def __init__(self, storage_dir: str = "knowledge_base"):
        self.storage_dir = storage_dir
        self.processor = ReferenceProcessor()
        
        # 创建存储目录
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # 内存存储
        self.documents = {}  # {doc_id: content}
        self.metadata = {}   # {doc_id: metadata}
        self.terminology = {}  # {term_id: {zh_term, en_term, ...}}
        self.references = {}  # {ref_id: reference_info}
        
        # 加载现有数据
        self._load_data()
    
    def _load_data(self):
        """加载现有数据"""
        try:
            # 加载文档
            docs_file = os.path.join(self.storage_dir, "documents.json")
            if os.path.exists(docs_file):
                with open(docs_file, 'r', encoding='utf-8') as f:
                    self.documents = json.load(f)
            
            # 加载元数据
            meta_file = os.path.join(self.storage_dir, "metadata.json")
            if os.path.exists(meta_file):
                with open(meta_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            
            # 加载术语
            term_file = os.path.join(self.storage_dir, "terminology.json")
            if os.path.exists(term_file):
                with open(term_file, 'r', encoding='utf-8') as f:
                    self.terminology = json.load(f)
            
            # 加载参考文档信息
            ref_file = os.path.join(self.storage_dir, "references.json")
            if os.path.exists(ref_file):
                with open(ref_file, 'r', encoding='utf-8') as f:
                    self.references = json.load(f)
            
            logger.info(f"加载了 {len(self.documents)} 个文档, {len(self.terminology)} 个术语")
            
        except Exception as e:
            logger.warning(f"加载数据失败: {e}")
    
    def _save_data(self):
        """保存数据"""
        try:
            # 保存文档
            with open(os.path.join(self.storage_dir, "documents.json"), 'w', encoding='utf-8') as f:
                json.dump(self.documents, f, ensure_ascii=False, indent=2)
            
            # 保存元数据
            with open(os.path.join(self.storage_dir, "metadata.json"), 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            
            # 保存术语
            with open(os.path.join(self.storage_dir, "terminology.json"), 'w', encoding='utf-8') as f:
                json.dump(self.terminology, f, ensure_ascii=False, indent=2)
            
            # 保存参考文档信息
            with open(os.path.join(self.storage_dir, "references.json"), 'w', encoding='utf-8') as f:
                json.dump(self.references, f, ensure_ascii=False, indent=2)
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
    
    def add_reference_pair(self, zh_doc_path: str, en_doc_path: str, 
                          reference_name: str = None) -> bool:
        """
        添加中英文参考文档对
        
        Args:
            zh_doc_path: 中文文档路径
            en_doc_path: 英文文档路径
            reference_name: 参考文档名称
            
        Returns:
            是否添加成功
        """
        try:
            # 验证文档
            zh_valid, zh_error = self.processor.validate_document(zh_doc_path)
            en_valid, en_error = self.processor.validate_document(en_doc_path)
            
            if not zh_valid:
                logger.error(f"中文文档验证失败: {zh_error}")
                return False
            
            if not en_valid:
                logger.error(f"英文文档验证失败: {en_error}")
                return False
            
            # 提取双语对照内容
            bilingual_pairs = self.processor.extract_bilingual_pairs(zh_doc_path, en_doc_path)
            
            if not bilingual_pairs:
                logger.warning("未能提取到双语对照内容")
                return False
            
            # 生成参考文档ID
            if not reference_name:
                reference_name = f"{os.path.basename(zh_doc_path)}_{os.path.basename(en_doc_path)}"
            
            ref_id = hashlib.md5(reference_name.encode()).hexdigest()[:12]
            
            # 删除现有的同名参考文档
            self._remove_reference(ref_id)
            
            # 记录参考文档信息
            self.references[ref_id] = {
                'reference_id': ref_id,
                'reference_name': reference_name,
                'zh_file': os.path.basename(zh_doc_path),
                'en_file': os.path.basename(en_doc_path),
                'zh_count': 0,
                'en_count': 0,
                'term_count': 0
            }
            
            # 添加双语对照内容
            for pair in bilingual_pairs:
                self._add_bilingual_pair(pair, ref_id, reference_name)
            
            # 提取并添加术语对照
            terminology_pairs = self.processor.extract_terminology_pairs(bilingual_pairs)
            for term_pair in terminology_pairs:
                self._add_terminology_pair(term_pair, ref_id, reference_name)
            
            # 保存数据
            self._save_data()
            
            logger.info(f"成功添加参考文档对: {reference_name} (ID: {ref_id})")
            return True
            
        except Exception as e:
            logger.error(f"添加参考文档对失败: {e}")
            return False
    
    def _remove_reference(self, ref_id: str):
        """删除指定的参考文档"""
        try:
            # 删除相关文档
            docs_to_remove = []
            for doc_id, metadata in self.metadata.items():
                if metadata.get('reference_id') == ref_id:
                    docs_to_remove.append(doc_id)
            
            for doc_id in docs_to_remove:
                if doc_id in self.documents:
                    del self.documents[doc_id]
                if doc_id in self.metadata:
                    del self.metadata[doc_id]
            
            # 删除相关术语
            terms_to_remove = []
            for term_id, term_data in self.terminology.items():
                if term_data.get('reference_id') == ref_id:
                    terms_to_remove.append(term_id)
            
            for term_id in terms_to_remove:
                del self.terminology[term_id]
            
            # 删除参考文档记录
            if ref_id in self.references:
                del self.references[ref_id]
            
            logger.info(f"删除了参考文档 {ref_id} 的 {len(docs_to_remove)} 个文档和 {len(terms_to_remove)} 个术语")
            
        except Exception as e:
            logger.warning(f"删除参考文档失败: {e}")
    
    def _add_bilingual_pair(self, pair: Dict, ref_id: str, reference_name: str):
        """添加双语对照到知识库"""
        try:
            zh_content = pair['zh_content']
            en_content = pair['en_content']
            
            if len(zh_content) < 20 and len(en_content) < 20:  # 跳过太短的内容
                return
            
            # 添加中文内容
            if zh_content:
                zh_id = f"{ref_id}-zh-{pair['page_number']}-{hashlib.md5(zh_content.encode()).hexdigest()[:8]}"
                
                self.documents[zh_id] = zh_content
                self.metadata[zh_id] = {
                    'reference_id': ref_id,
                    'reference_name': reference_name,
                    'language': 'zh',
                    'page_number': pair['page_number'],
                    'content_type': 'bilingual_pair',
                    'paired_content': en_content[:500],
                }
                
                self.references[ref_id]['zh_count'] += 1
            
            # 添加英文内容
            if en_content:
                en_id = f"{ref_id}-en-{pair['page_number']}-{hashlib.md5(en_content.encode()).hexdigest()[:8]}"
                
                self.documents[en_id] = en_content
                self.metadata[en_id] = {
                    'reference_id': ref_id,
                    'reference_name': reference_name,
                    'language': 'en',
                    'page_number': pair['page_number'],
                    'content_type': 'bilingual_pair',
                    'paired_content': zh_content[:500],
                }
                
                self.references[ref_id]['en_count'] += 1
            
        except Exception as e:
            logger.warning(f"添加双语对照失败: {e}")
    
    def _add_terminology_pair(self, term_pair: Dict, ref_id: str, reference_name: str):
        """添加术语对照到知识库"""
        try:
            zh_term = term_pair['zh_term']
            en_term = term_pair['en_term']
            
            combined_term = f"{zh_term} {en_term}"
            term_id = f"{ref_id}-term-{hashlib.md5(combined_term.encode()).hexdigest()[:8]}"
            
            self.terminology[term_id] = {
                'reference_id': ref_id,
                'reference_name': reference_name,
                'zh_term': zh_term,
                'en_term': en_term,
                'page_number': term_pair['page_number'],
                'confidence': term_pair['confidence']
            }
            
            self.references[ref_id]['term_count'] += 1
            
        except Exception as e:
            logger.warning(f"添加术语对照失败: {e}")
    
    def search_similar_content(self, query_text: str, target_language: str = 'zh', 
                             top_k: int = 5) -> List[Dict]:
        """
        搜索相似内容（使用简单的关键词匹配）
        
        Args:
            query_text: 查询文本
            target_language: 目标语言 ('zh' 或 'en')
            top_k: 返回结果数量
            
        Returns:
            相似内容列表
        """
        try:
            # 提取查询关键词
            query_keywords = self._extract_keywords(query_text)
            
            # 搜索匹配的文档
            matches = []
            for doc_id, content in self.documents.items():
                metadata = self.metadata.get(doc_id, {})
                
                # 过滤语言和内容类型
                if (metadata.get('language') == target_language and 
                    metadata.get('content_type') == 'bilingual_pair'):
                    
                    # 计算相似度分数
                    score = self._calculate_similarity(query_keywords, content)
                    
                    if score > 0:
                        matches.append({
                            'content': content,
                            'paired_content': metadata.get('paired_content', ''),
                            'metadata': metadata,
                            'similarity_score': score,
                            'doc_id': doc_id
                        })
            
            # 按相似度排序
            matches.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            # 返回前top_k个结果
            results = matches[:top_k]
            for i, result in enumerate(results, 1):
                result['rank'] = i
            
            logger.info(f"找到 {len(results)} 个相似内容")
            return results
            
        except Exception as e:
            logger.error(f"搜索相似内容失败: {e}")
            return []
    
    def search_terminology(self, term: str, source_language: str = 'zh') -> List[Dict]:
        """
        搜索术语翻译
        
        Args:
            term: 术语
            source_language: 源语言
            
        Returns:
            术语翻译列表
        """
        try:
            results = []
            
            for term_id, term_data in self.terminology.items():
                zh_term = term_data.get('zh_term', '')
                en_term = term_data.get('en_term', '')
                
                # 计算匹配度
                if source_language == 'zh':
                    similarity = self._calculate_term_similarity(term, zh_term)
                else:
                    similarity = self._calculate_term_similarity(term, en_term)
                
                if similarity > 0.3:  # 相似度阈值
                    results.append({
                        'zh_term': zh_term,
                        'en_term': en_term,
                        'similarity_score': similarity,
                        'confidence': term_data.get('confidence', 0),
                        'reference_name': term_data.get('reference_name', ''),
                        'page_number': term_data.get('page_number', 0)
                    })
            
            # 按相似度排序
            results.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            logger.info(f"找到 {len(results)} 个术语翻译")
            return results
            
        except Exception as e:
            logger.error(f"搜索术语翻译失败: {e}")
            return []
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取：分词并过滤停用词
        keywords = []
        
        # 中文分词（简单按字符分割）
        chinese_chars = re.findall(r'[\u4e00-\u9fff]+', text)
        for chars in chinese_chars:
            if len(chars) >= 2:
                keywords.extend([chars[i:i+2] for i in range(len(chars)-1)])
        
        # 英文分词
        english_words = re.findall(r'[a-zA-Z]+', text)
        keywords.extend([word.lower() for word in english_words if len(word) >= 3])
        
        return list(set(keywords))
    
    def _calculate_similarity(self, query_keywords: List[str], content: str) -> float:
        """计算相似度分数"""
        if not query_keywords:
            return 0
        
        content_lower = content.lower()
        matches = 0
        
        for keyword in query_keywords:
            if keyword in content_lower:
                matches += 1
        
        return matches / len(query_keywords)
    
    def _calculate_term_similarity(self, term1: str, term2: str) -> float:
        """计算术语相似度"""
        if not term1 or not term2:
            return 0
        
        term1_lower = term1.lower()
        term2_lower = term2.lower()
        
        # 完全匹配
        if term1_lower == term2_lower:
            return 1.0
        
        # 包含关系
        if term1_lower in term2_lower or term2_lower in term1_lower:
            return 0.8
        
        # 字符重叠度
        common_chars = set(term1_lower) & set(term2_lower)
        total_chars = set(term1_lower) | set(term2_lower)
        
        if total_chars:
            return len(common_chars) / len(total_chars)
        
        return 0
    
    def get_reference_statistics(self) -> Dict[str, int]:
        """获取参考知识库统计信息"""
        try:
            zh_count = sum(1 for meta in self.metadata.values() if meta.get('language') == 'zh')
            en_count = sum(1 for meta in self.metadata.values() if meta.get('language') == 'en')
            
            return {
                'total_documents': len(self.documents),
                'chinese_documents': zh_count,
                'english_documents': en_count,
                'terminology_pairs': len(self.terminology),
                'reference_count': len(self.references)
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'total_documents': 0,
                'chinese_documents': 0,
                'english_documents': 0,
                'terminology_pairs': 0,
                'reference_count': 0
            }
    
    def list_references(self) -> List[Dict]:
        """列出所有参考文档"""
        return list(self.references.values())
    
    def remove_reference(self, reference_id: str) -> bool:
        """删除指定的参考文档"""
        try:
            self._remove_reference(reference_id)
            self._save_data()
            logger.info(f"成功删除参考文档: {reference_id}")
            return True
        except Exception as e:
            logger.error(f"删除参考文档失败: {e}")
            return False
