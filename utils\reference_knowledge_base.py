"""
参考知识库模块 - 管理用户提供的中英文参考文档
"""
import os
import hashlib
from typing import List, Dict, Optional, Tuple
from sentence_transformers import SentenceTransformer
import chromadb
from chromadb.config import Settings
import numpy as np
import logging
from config import Config
from utils.reference_processor import ReferenceProcessor

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)

class ReferenceKnowledgeBase:
    """参考知识库管理器"""
    
    def __init__(self):
        self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        self.chroma_client = chromadb.PersistentClient(
            path=Config.CHROMA_PERSIST_DIRECTORY,
            settings=Settings(anonymized_telemetry=False)
        )
        self.collection_name = "reference_documents"
        self.processor = ReferenceProcessor()
        self._init_collection()
    
    def _init_collection(self):
        """初始化ChromaDB集合"""
        try:
            # 尝试获取现有集合
            self.collection = self.chroma_client.get_collection(self.collection_name)
            logger.info(f"已连接到现有集合: {self.collection_name}")
        except:
            # 创建新集合
            self.collection = self.chroma_client.create_collection(
                name=self.collection_name,
                metadata={"description": "User provided reference documents"}
            )
            logger.info(f"已创建新集合: {self.collection_name}")
    
    def add_reference_pair(self, zh_doc_path: str, en_doc_path: str, 
                          reference_name: str = None) -> bool:
        """
        添加中英文参考文档对
        
        Args:
            zh_doc_path: 中文文档路径
            en_doc_path: 英文文档路径
            reference_name: 参考文档名称
            
        Returns:
            是否添加成功
        """
        try:
            # 验证文档
            zh_valid, zh_error = self.processor.validate_document(zh_doc_path)
            en_valid, en_error = self.processor.validate_document(en_doc_path)
            
            if not zh_valid:
                logger.error(f"中文文档验证失败: {zh_error}")
                return False
            
            if not en_valid:
                logger.error(f"英文文档验证失败: {en_error}")
                return False
            
            # 提取双语对照内容
            bilingual_pairs = self.processor.extract_bilingual_pairs(zh_doc_path, en_doc_path)
            
            if not bilingual_pairs:
                logger.warning("未能提取到双语对照内容")
                return False
            
            # 生成参考文档ID
            if not reference_name:
                reference_name = f"{os.path.basename(zh_doc_path)}_{os.path.basename(en_doc_path)}"
            
            ref_id = hashlib.md5(reference_name.encode()).hexdigest()[:12]
            
            # 删除现有的同名参考文档
            self._remove_reference(ref_id)
            
            # 添加双语对照内容到知识库
            for pair in bilingual_pairs:
                self._add_bilingual_pair(pair, ref_id, reference_name)
            
            # 提取并添加术语对照
            terminology_pairs = self.processor.extract_terminology_pairs(bilingual_pairs)
            for term_pair in terminology_pairs:
                self._add_terminology_pair(term_pair, ref_id, reference_name)
            
            logger.info(f"成功添加参考文档对: {reference_name} (ID: {ref_id})")
            return True
            
        except Exception as e:
            logger.error(f"添加参考文档对失败: {e}")
            return False
    
    def _remove_reference(self, ref_id: str):
        """删除指定的参考文档"""
        try:
            # 查询现有文档
            results = self.collection.get(
                where={"reference_id": ref_id}
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"删除了 {len(results['ids'])} 个现有参考文档")
        
        except Exception as e:
            logger.warning(f"删除现有参考文档失败: {e}")
    
    def _add_bilingual_pair(self, pair: Dict, ref_id: str, reference_name: str):
        """添加双语对照到知识库"""
        try:
            zh_content = pair['zh_content']
            en_content = pair['en_content']
            
            if len(zh_content) < 20 and len(en_content) < 20:  # 跳过太短的内容
                return
            
            # 为中文内容生成嵌入
            if zh_content:
                zh_embedding = self.embedding_model.encode(zh_content).tolist()
                zh_id = f"{ref_id}-zh-{pair['page_number']}-{hashlib.md5(zh_content.encode()).hexdigest()[:8]}"
                
                zh_metadata = {
                    'reference_id': ref_id,
                    'reference_name': reference_name,
                    'language': 'zh',
                    'page_number': pair['page_number'],
                    'content_type': 'bilingual_pair',
                    'paired_content': en_content[:500],  # 存储对应的英文内容（截断）
                }
                
                self.collection.add(
                    ids=[zh_id],
                    embeddings=[zh_embedding],
                    documents=[zh_content],
                    metadatas=[zh_metadata]
                )
            
            # 为英文内容生成嵌入
            if en_content:
                en_embedding = self.embedding_model.encode(en_content).tolist()
                en_id = f"{ref_id}-en-{pair['page_number']}-{hashlib.md5(en_content.encode()).hexdigest()[:8]}"
                
                en_metadata = {
                    'reference_id': ref_id,
                    'reference_name': reference_name,
                    'language': 'en',
                    'page_number': pair['page_number'],
                    'content_type': 'bilingual_pair',
                    'paired_content': zh_content[:500],  # 存储对应的中文内容（截断）
                }
                
                self.collection.add(
                    ids=[en_id],
                    embeddings=[en_embedding],
                    documents=[en_content],
                    metadatas=[en_metadata]
                )
            
        except Exception as e:
            logger.warning(f"添加双语对照失败: {e}")
    
    def _add_terminology_pair(self, term_pair: Dict, ref_id: str, reference_name: str):
        """添加术语对照到知识库"""
        try:
            zh_term = term_pair['zh_term']
            en_term = term_pair['en_term']
            
            # 为术语对生成嵌入（使用中英文术语组合）
            combined_term = f"{zh_term} {en_term}"
            embedding = self.embedding_model.encode(combined_term).tolist()
            
            term_id = f"{ref_id}-term-{hashlib.md5(combined_term.encode()).hexdigest()[:8]}"
            
            metadata = {
                'reference_id': ref_id,
                'reference_name': reference_name,
                'language': 'bilingual',
                'page_number': term_pair['page_number'],
                'content_type': 'terminology',
                'zh_term': zh_term,
                'en_term': en_term,
                'confidence': term_pair['confidence']
            }
            
            self.collection.add(
                ids=[term_id],
                embeddings=[embedding],
                documents=[combined_term],
                metadatas=[metadata]
            )
            
        except Exception as e:
            logger.warning(f"添加术语对照失败: {e}")
    
    def search_similar_content(self, query_text: str, target_language: str = 'zh', 
                             top_k: int = 5) -> List[Dict]:
        """
        搜索相似内容
        
        Args:
            query_text: 查询文本
            target_language: 目标语言 ('zh' 或 'en')
            top_k: 返回结果数量
            
        Returns:
            相似内容列表
        """
        try:
            # 生成查询向量
            query_embedding = self.embedding_model.encode(query_text).tolist()
            
            # 构建查询条件
            where_condition = {
                "language": target_language,
                "content_type": "bilingual_pair"
            }
            
            # 执行搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_condition,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 格式化结果
            formatted_results = []
            for i, (doc, metadata, distance) in enumerate(zip(
                results['documents'][0], 
                results['metadatas'][0], 
                results['distances'][0]
            )):
                formatted_results.append({
                    'content': doc,
                    'paired_content': metadata.get('paired_content', ''),
                    'metadata': metadata,
                    'similarity_score': 1 - distance,  # 转换为相似度分数
                    'rank': i + 1
                })
            
            logger.info(f"找到 {len(formatted_results)} 个相似内容")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索相似内容失败: {e}")
            return []
    
    def search_terminology(self, term: str, source_language: str = 'zh') -> List[Dict]:
        """
        搜索术语翻译
        
        Args:
            term: 术语
            source_language: 源语言
            
        Returns:
            术语翻译列表
        """
        try:
            # 生成查询向量
            query_embedding = self.embedding_model.encode(term).tolist()
            
            # 搜索术语
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=10,
                where={"content_type": "terminology"},
                include=['documents', 'metadatas', 'distances']
            )
            
            # 格式化术语结果
            terminology_results = []
            for doc, metadata, distance in zip(
                results['documents'][0], 
                results['metadatas'][0], 
                results['distances'][0]
            ):
                if distance < 0.5:  # 只返回相似度较高的术语
                    terminology_results.append({
                        'zh_term': metadata.get('zh_term', ''),
                        'en_term': metadata.get('en_term', ''),
                        'similarity_score': 1 - distance,
                        'confidence': metadata.get('confidence', 0),
                        'reference_name': metadata.get('reference_name', ''),
                        'page_number': metadata.get('page_number', 0)
                    })
            
            # 按相似度排序
            terminology_results.sort(key=lambda x: x['similarity_score'], reverse=True)
            
            logger.info(f"找到 {len(terminology_results)} 个术语翻译")
            return terminology_results
            
        except Exception as e:
            logger.error(f"搜索术语翻译失败: {e}")
            return []
    
    def get_reference_statistics(self) -> Dict[str, int]:
        """获取参考知识库统计信息"""
        try:
            total_count = self.collection.count()
            
            # 按语言统计
            zh_results = self.collection.get(where={"language": "zh"})
            en_results = self.collection.get(where={"language": "en"})
            term_results = self.collection.get(where={"content_type": "terminology"})
            
            # 按参考文档统计
            all_results = self.collection.get(include=['metadatas'])
            references = set()
            for metadata in all_results['metadatas']:
                if metadata.get('reference_id'):
                    references.add(metadata['reference_id'])
            
            return {
                'total_documents': total_count,
                'chinese_documents': len(zh_results['ids']) if zh_results['ids'] else 0,
                'english_documents': len(en_results['ids']) if en_results['ids'] else 0,
                'terminology_pairs': len(term_results['ids']) if term_results['ids'] else 0,
                'reference_count': len(references)
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'total_documents': 0,
                'chinese_documents': 0,
                'english_documents': 0,
                'terminology_pairs': 0,
                'reference_count': 0
            }
    
    def list_references(self) -> List[Dict]:
        """列出所有参考文档"""
        try:
            all_results = self.collection.get(include=['metadatas'])
            references = {}
            
            for metadata in all_results['metadatas']:
                ref_id = metadata.get('reference_id')
                ref_name = metadata.get('reference_name')
                
                if ref_id and ref_name:
                    if ref_id not in references:
                        references[ref_id] = {
                            'reference_id': ref_id,
                            'reference_name': ref_name,
                            'zh_count': 0,
                            'en_count': 0,
                            'term_count': 0
                        }
                    
                    if metadata.get('language') == 'zh':
                        references[ref_id]['zh_count'] += 1
                    elif metadata.get('language') == 'en':
                        references[ref_id]['en_count'] += 1
                    elif metadata.get('content_type') == 'terminology':
                        references[ref_id]['term_count'] += 1
            
            return list(references.values())
            
        except Exception as e:
            logger.error(f"列出参考文档失败: {e}")
            return []
    
    def remove_reference(self, reference_id: str) -> bool:
        """删除指定的参考文档"""
        try:
            self._remove_reference(reference_id)
            logger.info(f"成功删除参考文档: {reference_id}")
            return True
        except Exception as e:
            logger.error(f"删除参考文档失败: {e}")
            return False
