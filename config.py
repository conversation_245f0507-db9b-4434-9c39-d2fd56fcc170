"""
配置文件 - 管理应用程序的所有配置参数
"""
import os
from dotenv import load_dotenv
from pathlib import Path

# 加载环境变量
load_dotenv()

class Config:
    """应用程序配置类"""
    
    # API配置
    OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
    OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
    
    # 模型配置
    DEFAULT_TRANSLATION_MODEL = os.getenv("DEFAULT_TRANSLATION_MODEL", "anthropic/claude-3.5-sonnet")
    DEFAULT_REVIEW_MODEL = os.getenv("DEFAULT_REVIEW_MODEL", "anthropic/claude-3.5-sonnet")
    BACKUP_MODEL = os.getenv("BACKUP_MODEL", "openai/gpt-4-turbo")
    
    # 成本控制
    MAX_COST_PER_DOCUMENT = float(os.getenv("MAX_COST_PER_DOCUMENT", "50.0"))
    COST_WARNING_THRESHOLD = float(os.getenv("COST_WARNING_THRESHOLD", "30.0"))
    
    # 数据库配置
    CHROMA_PERSIST_DIRECTORY = os.getenv("CHROMA_PERSIST_DIRECTORY", "./chroma_db")
    TRANSLATION_HISTORY_DB = os.getenv("TRANSLATION_HISTORY_DB", "./translation_history.db")
    
    # 爬虫配置
    CHROME_DRIVER_PATH = os.getenv("CHROME_DRIVER_PATH", "auto")
    HEADLESS_BROWSER = os.getenv("HEADLESS_BROWSER", "true").lower() == "true"
    HKEX_BASE_URL = "https://www1.hkexnews.hk"
    
    # 文件处理配置
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "50"))
    MAX_PAGES_PER_DOCUMENT = int(os.getenv("MAX_PAGES_PER_DOCUMENT", "200"))
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "5"))
    
    # 支持的文件类型
    SUPPORTED_INPUT_FORMATS = [".docx"]
    SUPPORTED_OUTPUT_FORMATS = [".docx"]
    
    # 翻译配置
    MAX_RETRY_ATTEMPTS = 5
    TRANSLATION_TIMEOUT = 300  # 5分钟
    
    # 目录配置
    BASE_DIR = Path(__file__).parent
    TEMP_DIR = BASE_DIR / "temp"
    KNOWLEDGE_BASE_DIR = BASE_DIR / "knowledge_base"
    TRANSLATION_HISTORY_DIR = BASE_DIR / "translation_history"
    
    # 创建必要的目录
    @classmethod
    def create_directories(cls):
        """创建必要的目录"""
        directories = [
            cls.TEMP_DIR,
            cls.KNOWLEDGE_BASE_DIR,
            cls.TRANSLATION_HISTORY_DIR,
            Path(cls.CHROMA_PERSIST_DIRECTORY).parent
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    # 验证配置
    @classmethod
    def validate_config(cls):
        """验证配置的有效性"""
        if not cls.OPENROUTER_API_KEY:
            raise ValueError("OPENROUTER_API_KEY 未设置，请检查 .env 文件")
        
        cls.create_directories()
        return True

# 模型价格配置（每1K tokens的价格，USD）
MODEL_PRICING = {
    "anthropic/claude-3.5-sonnet": {"input": 0.003, "output": 0.015},
    "anthropic/claude-3-haiku": {"input": 0.00025, "output": 0.00125},
    "openai/gpt-4-turbo": {"input": 0.01, "output": 0.03},
    "openai/gpt-4o": {"input": 0.005, "output": 0.015},
    "openai/gpt-3.5-turbo": {"input": 0.0005, "output": 0.0015},
}

# HKEx报告类型映射
HKEX_REPORT_TYPES = {
    "annual_report": "年报",
    "interim_report": "中期报告", 
    "quarterly_report": "季度报告",
    "announcement": "公告",
    "circular": "通函",
    "prospectus": "招股书"
}

# 翻译方向
TRANSLATION_DIRECTIONS = {
    "zh_to_en": "中文到英文",
    "en_to_zh": "英文到中文"
}
