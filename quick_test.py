#!/usr/bin/env python3
"""
快速测试文档信息提取
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试"""
    try:
        from docx import Document
        from utils.document_processor import DocumentProcessor
        
        # 创建测试文档
        doc = Document()
        doc.add_heading('小米集团', 0)
        doc.add_heading('Xiaomi Corporation', 1)
        doc.add_paragraph('股票代码：1810')
        doc.add_paragraph('2023年年度报告')
        
        test_file = "quick_test.docx"
        doc.save(test_file)
        
        # 测试提取
        processor = DocumentProcessor()
        result = processor.extract_company_info(test_file)
        
        print("提取结果:")
        for key, value in result.items():
            print(f"  {key}: {value}")
        
        # 清理
        if os.path.exists(test_file):
            os.remove(test_file)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    quick_test()
