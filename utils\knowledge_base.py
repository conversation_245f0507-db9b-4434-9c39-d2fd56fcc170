"""
知识库模块 - PDF解析、向量化存储、相似度检索
"""
import os
import hashlib
from typing import List, Dict, Optional, Tuple
import PyPDF2
import pdfplumber
from sentence_transformers import SentenceTransformer
import chromadb
from chromadb.config import Settings
import numpy as np
import logging
from config import Config

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)

class KnowledgeBase:
    """知识库管理器"""
    
    def __init__(self):
        self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        self.chroma_client = chromadb.PersistentClient(
            path=Config.CHROMA_PERSIST_DIRECTORY,
            settings=Settings(anonymized_telemetry=False)
        )
        self.collection_name = "hkex_reports"
        self._init_collection()
    
    def _init_collection(self):
        """初始化ChromaDB集合"""
        try:
            # 尝试获取现有集合
            self.collection = self.chroma_client.get_collection(self.collection_name)
            logger.info(f"已连接到现有集合: {self.collection_name}")
        except:
            # 创建新集合
            self.collection = self.chroma_client.create_collection(
                name=self.collection_name,
                metadata={"description": "HKEx reports knowledge base"}
            )
            logger.info(f"已创建新集合: {self.collection_name}")
    
    def extract_text_from_pdf(self, pdf_path: str) -> List[Dict[str, str]]:
        """
        从PDF提取文本内容
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            文本块列表，每个块包含页码和内容
        """
        try:
            text_blocks = []
            
            # 使用pdfplumber提取文本（更好的表格支持）
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        # 提取文本
                        text = page.extract_text()
                        if text and text.strip():
                            text_blocks.append({
                                'page_number': page_num,
                                'content': text.strip(),
                                'source_file': os.path.basename(pdf_path)
                            })
                        
                        # 提取表格
                        tables = page.extract_tables()
                        for table_idx, table in enumerate(tables):
                            if table:
                                table_text = self._table_to_text(table)
                                if table_text:
                                    text_blocks.append({
                                        'page_number': page_num,
                                        'content': f"表格 {table_idx + 1}:\n{table_text}",
                                        'source_file': os.path.basename(pdf_path),
                                        'content_type': 'table'
                                    })
                    
                    except Exception as e:
                        logger.warning(f"提取第 {page_num} 页内容失败: {e}")
                        continue
            
            logger.info(f"从 {pdf_path} 提取了 {len(text_blocks)} 个文本块")
            return text_blocks
            
        except Exception as e:
            logger.error(f"PDF文本提取失败: {e}")
            return []
    
    def _table_to_text(self, table: List[List[str]]) -> str:
        """将表格转换为文本格式"""
        try:
            if not table:
                return ""
            
            text_lines = []
            for row in table:
                if row:
                    # 过滤空值并连接单元格
                    cells = [str(cell).strip() if cell else "" for cell in row]
                    if any(cells):  # 至少有一个非空单元格
                        text_lines.append(" | ".join(cells))
            
            return "\n".join(text_lines)
        except:
            return ""
    
    def add_bilingual_reports(self, zh_pdf_path: str, en_pdf_path: str, 
                            company_info: Dict[str, str]) -> bool:
        """
        添加双语报告到知识库
        
        Args:
            zh_pdf_path: 中文PDF路径
            en_pdf_path: 英文PDF路径
            company_info: 公司信息
            
        Returns:
            是否添加成功
        """
        try:
            # 提取中文内容
            zh_blocks = []
            if zh_pdf_path and os.path.exists(zh_pdf_path):
                zh_blocks = self.extract_text_from_pdf(zh_pdf_path)
            
            # 提取英文内容
            en_blocks = []
            if en_pdf_path and os.path.exists(en_pdf_path):
                en_blocks = self.extract_text_from_pdf(en_pdf_path)
            
            if not zh_blocks and not en_blocks:
                logger.warning("没有提取到任何内容")
                return False
            
            # 创建文档ID
            doc_id = self._generate_doc_id(company_info)
            
            # 删除现有的同公司文档
            self._remove_company_documents(company_info.get('stock_code', ''))
            
            # 添加中文内容
            for block in zh_blocks:
                self._add_text_block(block, company_info, 'zh', doc_id)
            
            # 添加英文内容
            for block in en_blocks:
                self._add_text_block(block, company_info, 'en', doc_id)
            
            logger.info(f"成功添加双语报告到知识库: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加双语报告失败: {e}")
            return False
    
    def _generate_doc_id(self, company_info: Dict[str, str]) -> str:
        """生成文档ID"""
        key_info = f"{company_info.get('stock_code', '')}-{company_info.get('report_type', '')}-{company_info.get('report_year', '')}"
        return hashlib.md5(key_info.encode()).hexdigest()[:12]
    
    def _remove_company_documents(self, stock_code: str):
        """删除指定公司的现有文档"""
        try:
            if not stock_code:
                return
            
            # 查询现有文档
            results = self.collection.get(
                where={"stock_code": stock_code}
            )
            
            if results['ids']:
                self.collection.delete(ids=results['ids'])
                logger.info(f"删除了 {len(results['ids'])} 个现有文档")
        
        except Exception as e:
            logger.warning(f"删除现有文档失败: {e}")
    
    def _add_text_block(self, block: Dict[str, str], company_info: Dict[str, str], 
                       language: str, doc_id: str):
        """添加文本块到知识库"""
        try:
            content = block['content']
            if len(content) < 50:  # 跳过太短的内容
                return
            
            # 生成嵌入向量
            embedding = self.embedding_model.encode(content).tolist()
            
            # 创建唯一ID
            block_id = f"{doc_id}-{language}-{block['page_number']}-{hashlib.md5(content.encode()).hexdigest()[:8]}"
            
            # 准备元数据
            metadata = {
                'stock_code': company_info.get('stock_code', ''),
                'company_name_zh': company_info.get('company_name_zh', ''),
                'company_name_en': company_info.get('company_name_en', ''),
                'report_type': company_info.get('report_type', ''),
                'report_year': company_info.get('report_year', ''),
                'language': language,
                'page_number': block['page_number'],
                'source_file': block['source_file'],
                'content_type': block.get('content_type', 'text'),
                'doc_id': doc_id
            }
            
            # 添加到集合
            self.collection.add(
                ids=[block_id],
                embeddings=[embedding],
                documents=[content],
                metadatas=[metadata]
            )
            
        except Exception as e:
            logger.warning(f"添加文本块失败: {e}")
    
    def search_similar_content(self, query_text: str, company_info: Dict[str, str], 
                             target_language: str = 'zh', top_k: int = 5) -> List[Dict]:
        """
        搜索相似内容
        
        Args:
            query_text: 查询文本
            company_info: 公司信息
            target_language: 目标语言
            top_k: 返回结果数量
            
        Returns:
            相似内容列表
        """
        try:
            # 生成查询向量
            query_embedding = self.embedding_model.encode(query_text).tolist()
            
            # 构建查询条件
            where_condition = {
                "language": target_language
            }
            
            # 优先搜索同公司的内容
            stock_code = company_info.get('stock_code', '')
            if stock_code:
                where_condition["stock_code"] = stock_code
            
            # 执行搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                where=where_condition,
                include=['documents', 'metadatas', 'distances']
            )
            
            # 如果同公司结果不足，搜索其他公司
            if len(results['documents'][0]) < top_k and stock_code:
                additional_results = self.collection.query(
                    query_embeddings=[query_embedding],
                    n_results=top_k - len(results['documents'][0]),
                    where={"language": target_language, "stock_code": {"$ne": stock_code}},
                    include=['documents', 'metadatas', 'distances']
                )
                
                # 合并结果
                if additional_results['documents'][0]:
                    results['documents'][0].extend(additional_results['documents'][0])
                    results['metadatas'][0].extend(additional_results['metadatas'][0])
                    results['distances'][0].extend(additional_results['distances'][0])
            
            # 格式化结果
            formatted_results = []
            for i, (doc, metadata, distance) in enumerate(zip(
                results['documents'][0], 
                results['metadatas'][0], 
                results['distances'][0]
            )):
                formatted_results.append({
                    'content': doc,
                    'metadata': metadata,
                    'similarity_score': 1 - distance,  # 转换为相似度分数
                    'rank': i + 1
                })
            
            logger.info(f"找到 {len(formatted_results)} 个相似内容")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索相似内容失败: {e}")
            return []
    
    def get_translation_pairs(self, company_info: Dict[str, str], 
                            content_type: str = 'text') -> List[Tuple[str, str]]:
        """
        获取翻译对照
        
        Args:
            company_info: 公司信息
            content_type: 内容类型
            
        Returns:
            (中文, 英文) 翻译对列表
        """
        try:
            stock_code = company_info.get('stock_code', '')
            
            # 获取中文内容
            zh_results = self.collection.get(
                where={
                    "stock_code": stock_code,
                    "language": "zh",
                    "content_type": content_type
                },
                include=['documents', 'metadatas']
            )
            
            # 获取英文内容
            en_results = self.collection.get(
                where={
                    "stock_code": stock_code,
                    "language": "en", 
                    "content_type": content_type
                },
                include=['documents', 'metadatas']
            )
            
            # 按页码匹配翻译对
            translation_pairs = []
            zh_by_page = {}
            en_by_page = {}
            
            # 组织中文内容
            for doc, metadata in zip(zh_results['documents'], zh_results['metadatas']):
                page_num = metadata['page_number']
                if page_num not in zh_by_page:
                    zh_by_page[page_num] = []
                zh_by_page[page_num].append(doc)
            
            # 组织英文内容
            for doc, metadata in zip(en_results['documents'], en_results['metadatas']):
                page_num = metadata['page_number']
                if page_num not in en_by_page:
                    en_by_page[page_num] = []
                en_by_page[page_num].append(doc)
            
            # 匹配翻译对
            for page_num in zh_by_page:
                if page_num in en_by_page:
                    zh_content = " ".join(zh_by_page[page_num])
                    en_content = " ".join(en_by_page[page_num])
                    translation_pairs.append((zh_content, en_content))
            
            logger.info(f"找到 {len(translation_pairs)} 个翻译对")
            return translation_pairs
            
        except Exception as e:
            logger.error(f"获取翻译对失败: {e}")
            return []
    
    def get_knowledge_base_stats(self) -> Dict[str, int]:
        """获取知识库统计信息"""
        try:
            total_count = self.collection.count()
            
            # 按语言统计
            zh_results = self.collection.get(where={"language": "zh"})
            en_results = self.collection.get(where={"language": "en"})
            
            # 按公司统计
            all_results = self.collection.get(include=['metadatas'])
            companies = set()
            for metadata in all_results['metadatas']:
                if metadata.get('stock_code'):
                    companies.add(metadata['stock_code'])
            
            return {
                'total_documents': total_count,
                'chinese_documents': len(zh_results['ids']) if zh_results['ids'] else 0,
                'english_documents': len(en_results['ids']) if en_results['ids'] else 0,
                'companies_count': len(companies)
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'total_documents': 0,
                'chinese_documents': 0,
                'english_documents': 0,
                'companies_count': 0
            }
