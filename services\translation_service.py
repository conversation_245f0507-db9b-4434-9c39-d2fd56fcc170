"""
翻译服务 - 协调多智能体翻译流程
"""

import os
import time
from typing import Dict, List, Optional, Tuple, Callable
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from utils.reference_processor import ReferenceProcessor
from utils.reference_knowledge_base import ReferenceKnowledgeBase
from utils.cost_estimator import CostEstimator
from utils.translation_history import TranslationHistory
from agents.translation_agent import TranslationAgent
from agents.review_agent import ReviewAgent
from config import Config

# 确保services目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)


class TranslationService:
    """翻译服务主类"""

    def __init__(self):
        self.doc_processor = ReferenceProcessor()
        self.knowledge_base = ReferenceKnowledgeBase()
        self.cost_estimator = CostEstimator()
        self.history = TranslationHistory()
        self.translation_agent = TranslationAgent()
        self.review_agent = ReviewAgent()

        # 进度回调函数
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None

        # 线程锁
        self._lock = threading.Lock()

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback

    def _update_progress(self, current: int, total: int, message: str = ""):
        """更新进度"""
        if self.progress_callback:
            try:
                progress = current / total if total > 0 else 0
                self.progress_callback(progress, message)
            except Exception as e:
                logger.warning(f"进度回调失败: {e}")

    def _update_status(self, status: str):
        """更新状态"""
        if self.status_callback:
            try:
                self.status_callback(status)
            except Exception as e:
                logger.warning(f"状态回调失败: {e}")

    def translate_document(
        self, file_path: str, translation_direction: str, output_path: str = None
    ) -> Dict:
        """
        翻译文档主流程

        Args:
            file_path: 输入文件路径
            translation_direction: 翻译方向
            output_path: 输出文件路径

        Returns:
            翻译结果
        """
        try:
            start_time = time.time()
            self._update_status("开始翻译流程...")

            # 1. 验证文档
            self._update_progress(0, 100, "验证文档...")
            is_valid, error_msg = self.doc_processor.validate_document(file_path)
            if not is_valid:
                return {"success": False, "error": error_msg}

            # 2. 提取公司信息
            self._update_progress(5, 100, "提取公司信息...")
            company_info = self.doc_processor.extract_company_info(file_path)
            if not company_info:
                return {"success": False, "error": "无法提取公司信息"}

            # 3. 分割文档
            self._update_progress(10, 100, "分析文档结构...")
            pages = self.doc_processor.split_document_by_pages(file_path)
            if not pages:
                return {"success": False, "error": "文档分割失败"}

            # 4. 成本估算
            self._update_progress(15, 100, "估算翻译成本...")
            cost_estimate = self.cost_estimator.estimate_document_cost(
                pages, translation_direction
            )

            # 检查预算
            budget_status = self.cost_estimator.check_budget_status(
                cost_estimate.get("estimated_cost_with_buffer", 0)
            )

            if not budget_status.get("can_proceed", False):
                return {
                    "success": False,
                    "error": "预算超限",
                    "budget_status": budget_status,
                    "cost_estimate": cost_estimate,
                }

            # 5. 创建翻译项目
            project_id = self.history.create_project(
                company_info, translation_direction, file_path, len(pages)
            )

            # 6. 构建知识库
            self._update_progress(20, 100, "构建知识库...")
            knowledge_ready = self._prepare_knowledge_base(company_info)

            # 7. 执行翻译
            self._update_progress(25, 100, "开始翻译...")
            translation_results = self._translate_pages(
                pages, company_info, translation_direction, project_id
            )

            # 8. 生成输出文档
            self._update_progress(90, 100, "生成输出文档...")
            if not output_path:
                output_path = self._generate_output_path(
                    file_path, translation_direction
                )

            output_file = self.doc_processor.create_translated_document(
                file_path, translation_results, output_path
            )

            # 9. 计算实际成本
            cost_tracking = self.cost_estimator.track_actual_cost(
                translation_results, company_info
            )

            # 10. 更新项目状态
            total_cost = cost_tracking.get("total_cost", 0)
            processing_time = time.time() - start_time

            self.history.update_project_status(
                project_id, "completed", output_file, total_cost, processing_time
            )

            # 11. 生成质量报告
            quality_report = self.review_agent.final_quality_check(translation_results)

            self._update_progress(100, 100, "翻译完成!")

            return {
                "success": True,
                "project_id": project_id,
                "output_file": output_file,
                "company_info": company_info,
                "translation_results": translation_results,
                "cost_estimate": cost_estimate,
                "actual_cost": total_cost,
                "processing_time": processing_time,
                "quality_report": quality_report,
                "knowledge_base_ready": knowledge_ready,
            }

        except Exception as e:
            logger.error(f"翻译流程失败: {e}")
            self._update_status(f"翻译失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _prepare_knowledge_base(self, company_info: Dict) -> bool:
        """准备知识库"""
        try:
            # 爬取同公司的双语报告
            zh_path, en_path = self.scraper.get_latest_bilingual_reports(company_info)

            if zh_path or en_path:
                # 添加到知识库
                success = self.knowledge_base.add_bilingual_reports(
                    zh_path, en_path, company_info
                )
                if success:
                    logger.info("知识库构建成功")
                    return True

            logger.warning("未能获取参考报告，将使用现有知识库")
            return False

        except Exception as e:
            logger.error(f"知识库准备失败: {e}")
            return False

    def _translate_pages(
        self,
        pages: List[Dict],
        company_info: Dict,
        translation_direction: str,
        project_id: str,
    ) -> List[Dict]:
        """翻译所有页面"""
        try:
            translated_pages = []
            total_pages = len(pages)

            for i, page in enumerate(pages, 1):
                self._update_progress(
                    25 + int(60 * i / total_pages),
                    100,
                    f"翻译第 {i}/{total_pages} 页...",
                )

                # 翻译单页
                page_result = self._translate_single_page(
                    page, company_info, translation_direction, project_id
                )

                translated_pages.append(page_result)

                # 保存页面结果
                self.history.save_page_translation(project_id, page_result)

            return translated_pages

        except Exception as e:
            logger.error(f"页面翻译失败: {e}")
            raise

    def _translate_single_page(
        self,
        page: Dict,
        company_info: Dict,
        translation_direction: str,
        project_id: str,
    ) -> Dict:
        """翻译单个页面（包含多智能体协作）"""
        try:
            # 1. 获取参考翻译
            target_language = "en" if translation_direction == "zh_to_en" else "zh"
            reference_translations = self.knowledge_base.search_similar_content(
                page.get("content", ""), company_info, target_language
            )

            # 2. 初始翻译
            translation_result = self.translation_agent.translate_page(
                page, reference_translations, translation_direction, company_info
            )

            if not translation_result.get("success", False):
                return translation_result

            # 3. 校对循环（最多5次）
            revision_count = 0
            max_revisions = Config.MAX_RETRY_ATTEMPTS

            while revision_count < max_revisions:
                # 校对翻译
                review_result = self.review_agent.review_translation(
                    translation_result,
                    reference_translations,
                    company_info,
                    translation_direction,
                )

                # 更新翻译结果
                translation_result.update(
                    {
                        "review_score": review_result.get("review_score", 0),
                        "consistency_score": review_result.get("consistency_score", 0),
                        "accuracy_score": review_result.get("accuracy_score", 0),
                        "issues_found": review_result.get("issues_found", []),
                        "suggestions": review_result.get("suggestions", []),
                    }
                )

                # 检查是否需要修订
                if not review_result.get("needs_revision", False):
                    logger.info(f"第 {page.get('page_number', '?')} 页校对通过")
                    break

                # 生成修订反馈
                feedback = self.review_agent.generate_revision_feedback(
                    review_result, reference_translations
                )

                # 修订翻译
                translation_result = self.translation_agent.revise_translation(
                    translation_result, feedback, reference_translations
                )

                revision_count += 1
                logger.info(
                    f"第 {page.get('page_number', '?')} 页第 {revision_count} 次修订完成"
                )

            # 记录修订次数
            translation_result["revision_count"] = revision_count
            translation_result["needs_revision"] = revision_count >= max_revisions

            return translation_result

        except Exception as e:
            logger.error(f"单页翻译失败: {e}")
            return {
                "page_number": page.get("page_number"),
                "success": False,
                "error": str(e),
            }

    def _generate_output_path(self, input_path: str, translation_direction: str) -> str:
        """生成输出文件路径"""
        try:
            input_file = os.path.basename(input_path)
            name, ext = os.path.splitext(input_file)

            direction_suffix = (
                "zh_to_en" if translation_direction == "zh_to_en" else "en_to_zh"
            )
            timestamp = time.strftime("%Y%m%d_%H%M%S")

            output_name = f"{name}_{direction_suffix}_{timestamp}{ext}"
            output_path = os.path.join(Config.TRANSLATION_HISTORY_DIR, output_name)

            return output_path

        except Exception as e:
            logger.error(f"生成输出路径失败: {e}")
            return f"translated_{int(time.time())}.docx"

    def batch_translate(
        self, file_paths: List[str], translation_direction: str
    ) -> List[Dict]:
        """
        批量翻译

        Args:
            file_paths: 文件路径列表
            translation_direction: 翻译方向

        Returns:
            批量翻译结果
        """
        try:
            results = []
            total_files = len(file_paths)

            for i, file_path in enumerate(file_paths, 1):
                self._update_status(f"处理第 {i}/{total_files} 个文件...")

                # 翻译单个文件
                result = self.translate_document(file_path, translation_direction)
                result["file_index"] = i
                result["input_file"] = file_path

                results.append(result)

                # 如果失败，记录但继续处理下一个
                if not result.get("success", False):
                    logger.warning(
                        f"文件 {file_path} 翻译失败: {result.get('error', '未知错误')}"
                    )

            # 生成批量处理报告
            successful_count = sum(1 for r in results if r.get("success", False))
            total_cost = sum(
                r.get("actual_cost", 0) for r in results if r.get("success", False)
            )

            batch_report = {
                "total_files": total_files,
                "successful_files": successful_count,
                "failed_files": total_files - successful_count,
                "success_rate": (
                    successful_count / total_files if total_files > 0 else 0
                ),
                "total_cost": total_cost,
                "results": results,
            }

            logger.info(
                f"批量翻译完成: {successful_count}/{total_files} 成功，总成本 ${total_cost:.2f}"
            )
            return batch_report

        except Exception as e:
            logger.error(f"批量翻译失败: {e}")
            return {"success": False, "error": str(e), "results": []}

    def get_translation_preview(self, file_path: str) -> Dict:
        """
        获取翻译预览信息

        Args:
            file_path: 文件路径

        Returns:
            预览信息
        """
        try:
            # 验证文档
            is_valid, error_msg = self.doc_processor.validate_document(file_path)
            if not is_valid:
                return {"success": False, "error": error_msg}

            # 提取基本信息
            company_info = self.doc_processor.extract_company_info(file_path)
            pages = self.doc_processor.split_document_by_pages(file_path)

            # 成本估算
            cost_estimate_zh_en = self.cost_estimator.estimate_document_cost(
                pages, "zh_to_en"
            )
            cost_estimate_en_zh = self.cost_estimator.estimate_document_cost(
                pages, "en_to_zh"
            )

            # 知识库统计
            kb_stats = self.knowledge_base.get_knowledge_base_stats()

            preview = {
                "success": True,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_size_mb": os.path.getsize(file_path) / (1024 * 1024),
                    "total_pages": len(pages),
                    "estimated_content_length": sum(
                        len(p.get("content", "")) for p in pages
                    ),
                },
                "company_info": company_info,
                "cost_estimates": {
                    "zh_to_en": cost_estimate_zh_en,
                    "en_to_zh": cost_estimate_en_zh,
                },
                "knowledge_base_stats": kb_stats,
                "sample_content": (
                    pages[0].get("content", "")[:500] + "..." if pages else ""
                ),
            }

            return preview

        except Exception as e:
            logger.error(f"获取预览失败: {e}")
            return {"success": False, "error": str(e)}
