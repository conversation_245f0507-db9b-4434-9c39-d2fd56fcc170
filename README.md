# 港交所上市公司披露报告翻译系统

基于多智能体架构的专业翻译系统，专门用于香港上市公司披露报告的中英文互译。系统具备术语一致性保证、格式完整保留、成本控制和翻译历史管理等功能。

## ✨ 主要特性

### 🤖 多智能体架构
- **翻译智能体**: 负责内容翻译，参考历史译法
- **校对智能体**: 负责质量校对，确保术语一致性
- **最多5次修订**: 自动迭代优化翻译质量

### 📚 智能知识库
- **动态构建**: 根据待翻译文件自动爬取同公司历史报告
- **术语一致性**: 优先使用同公司过往译法
- **向量检索**: 基于语义相似度匹配参考翻译

### 💰 成本控制
- **预估成本**: 翻译前准确估算API调用成本
- **预算管理**: 设置成本上限和警告阈值
- **使用统计**: 详细的成本分析和使用趋势

### 📄 格式保留
- **完整保留**: 保持原文档的段落、表格、格式
- **DOCX支持**: 输入输出均为DOCX格式
- **分页处理**: 按页翻译，便于质量控制

### 📊 翻译管理
- **历史记录**: 完整的翻译项目历史
- **版本管理**: 支持多版本翻译结果
- **批量处理**: 支持多文档批量翻译

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- Chrome浏览器（用于网页爬虫）
- OpenRouter API密钥

### 2. 安装配置

```bash
# 克隆项目
git clone <repository-url>
cd hkex-translation-system

# 复制配置文件
cp .env.example .env

# 编辑配置文件，设置API密钥
# 必须设置: OPENROUTER_API_KEY
nano .env
```

### 3. 启动系统

```bash
# 使用启动脚本（推荐）
python run.py

# 或直接启动应用
python app.py
```

### 4. 访问界面

打开浏览器访问: http://localhost:7860

## 📖 使用指南

### 单文档翻译

1. **上传文件**: 选择DOCX格式的披露报告
2. **选择方向**: 中文→英文 或 英文→中文
3. **预览文档**: 查看文档信息、公司信息、成本估算
4. **开始翻译**: 确认后点击开始翻译
5. **下载结果**: 翻译完成后下载结果文件

### 批量翻译

1. **上传多文件**: 选择多个DOCX文件
2. **选择方向**: 设置统一的翻译方向
3. **开始处理**: 系统将依次处理所有文件
4. **查看结果**: 查看批量处理报告和下载链接

### 翻译历史

- **历史记录**: 查看所有翻译项目的历史
- **成本统计**: 查看使用成本和趋势分析
- **项目详情**: 查看具体项目的详细信息

## ⚙️ 配置说明

### 环境变量配置

```bash
# OpenRouter API配置
OPENROUTER_API_KEY=your_api_key_here

# 模型配置
DEFAULT_TRANSLATION_MODEL=anthropic/claude-3.5-sonnet
DEFAULT_REVIEW_MODEL=anthropic/claude-3.5-sonnet

# 成本控制
MAX_COST_PER_DOCUMENT=50.0
COST_WARNING_THRESHOLD=30.0

# 文件处理
MAX_FILE_SIZE_MB=50
MAX_PAGES_PER_DOCUMENT=200
```

### 支持的模型

- `anthropic/claude-3.5-sonnet` (推荐)
- `anthropic/claude-3-haiku` (经济型)
- `openai/gpt-4-turbo`
- `openai/gpt-4o`
- `openai/gpt-3.5-turbo`

## 🏗️ 系统架构

```
├── app.py                 # Gradio主界面
├── run.py                 # 启动脚本
├── config.py              # 配置管理
├── agents/                # 智能体模块
│   ├── translation_agent.py  # 翻译智能体
│   └── review_agent.py        # 校对智能体
├── services/              # 服务层
│   └── translation_service.py # 翻译服务
├── utils/                 # 工具模块
│   ├── document_processor.py  # 文档处理
│   ├── hkex_scraper.py        # 网页爬虫
│   ├── knowledge_base.py      # 知识库管理
│   ├── cost_estimator.py      # 成本估算
│   └── translation_history.py # 历史管理
└── requirements.txt       # 依赖列表
```

## 🔧 技术栈

- **界面框架**: Gradio
- **文档处理**: python-docx
- **网页爬虫**: Selenium + BeautifulSoup
- **向量数据库**: ChromaDB
- **嵌入模型**: SentenceTransformers
- **PDF处理**: pdfplumber
- **数据库**: SQLite
- **AI模型**: OpenRouter API

## 📝 工作流程

### 翻译流程

1. **文档分析**: 提取公司信息、分割页面
2. **知识库构建**: 爬取同公司历史报告
3. **成本估算**: 计算预期翻译成本
4. **逐页翻译**: 
   - 检索相似内容
   - 翻译智能体翻译
   - 校对智能体校对
   - 必要时修订（最多5次）
5. **文档生成**: 保持格式生成结果文档
6. **历史记录**: 保存翻译历史和统计

### 质量保证

- **术语一致性**: 优先使用历史译法
- **多轮校对**: 最多5次修订循环
- **质量评分**: 准确性、一致性、专业性评分
- **人工审核**: 支持结果预览和确认

## 💡 使用建议

### 最佳实践

1. **首次使用**: 先处理几个同类报告建立知识库
2. **成本控制**: 大文档建议先预览成本估算
3. **批量处理**: 相同公司的报告建议批量处理
4. **质量检查**: 翻译完成后建议人工抽查

### 注意事项

- 首次运行会下载AI模型，需要较长时间
- 网络爬虫功能需要稳定的网络连接
- 大文档翻译可能需要较长时间和较高成本
- 建议在稳定的网络环境下使用

## 🐛 故障排除

### 常见问题

1. **Chrome Driver错误**
   ```bash
   # 安装Chrome浏览器
   # 或禁用爬虫功能，仅使用翻译功能
   ```

2. **API密钥错误**
   ```bash
   # 检查.env文件中的OPENROUTER_API_KEY设置
   # 确保API密钥有效且有足够余额
   ```

3. **内存不足**
   ```bash
   # 减少批量处理的文件数量
   # 或增加系统内存
   ```

4. **文档格式错误**
   ```bash
   # 确保输入文件为有效的DOCX格式
   # 检查文件是否损坏
   ```

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 支持

如有问题，请通过以下方式联系：

- 提交GitHub Issue
- 发送邮件至项目维护者

---

**免责声明**: 本系统仅供学习和研究使用，请确保遵守相关法律法规和API服务条款。翻译结果仅供参考，重要文档建议人工审核。
