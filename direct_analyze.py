from docx import Document
import re

# 分析真实文档
doc = Document("2022_ar_e.docx")

print("=== 基本信息 ===")
print(f"段落数: {len(doc.paragraphs)}")
print(f"表格数: {len(doc.tables)}")

# 显示前20段非空内容
print("\n=== 前20段内容 ===")
count = 0
for i, p in enumerate(doc.paragraphs):
    text = p.text.strip()
    if text and count < 20:
        print(f"{count+1:2d}. {text[:80]}{'...' if len(text) > 80 else ''}")
        count += 1

# 显示前2个表格的内容
print("\n=== 表格内容 ===")
for table_idx, table in enumerate(doc.tables[:2]):
    print(f"表格 {table_idx + 1}:")
    for row_idx, row in enumerate(table.rows[:3]):
        cells = [cell.text.strip() for cell in row.cells if cell.text.strip()]
        if cells:
            print(f"  行{row_idx+1}: {' | '.join(cells[:3])}")

# 收集所有文本进行模式匹配
all_text = ""
for p in doc.paragraphs[:100]:
    all_text += p.text + "\n"

for table in doc.tables[:5]:
    for row in table.rows:
        for cell in row.cells:
            all_text += cell.text + "\n"

print("\n=== 模式匹配测试 ===")

# 测试各种股票代码模式
stock_patterns = [
    (r"股票代码[：:\s]*(\d{4,5})", "股票代码"),
    (r"Stock Code[：:\s]*(\d{4,5})", "Stock Code"),
    (r"代码[：:\s]*(\d{4,5})", "代码"),
    (r"(\d{4})\.HK", ".HK格式"),
    (r"HK(\d{4})", "HK前缀"),
    (r"证券代码[：:\s]*(\d{4,5})", "证券代码"),
    (r"\b(\d{4})\b", "4位数字"),
]

print("股票代码搜索:")
for pattern, desc in stock_patterns:
    matches = re.findall(pattern, all_text, re.IGNORECASE)
    if matches:
        # 过滤年份
        filtered = [m for m in matches if not m.startswith('20')]
        if filtered:
            print(f"  {desc}: {filtered[:3]}")

# 测试公司名称模式
print("\n公司名称搜索:")
name_patterns = [
    (r"([^，。\n]{3,50}(?:有限公司|Limited|Ltd))", "标准公司名"),
    (r"([^，。\n]{3,30}(?:集团|控股|Holdings|Group))", "集团控股"),
]

for pattern, desc in name_patterns:
    matches = re.findall(pattern, all_text)
    if matches:
        unique = list(set(matches))[:3]
        print(f"  {desc}: {unique}")

# 测试年份
print("\n年份搜索:")
year_patterns = [
    (r"(\d{4})年", "年"),
    (r"Year (\d{4})", "Year"),
    (r"20\d{2}", "所有20xx"),
]

for pattern, desc in year_patterns:
    matches = re.findall(pattern, all_text)
    if matches:
        unique = sorted(list(set(matches)))
        print(f"  {desc}: {unique}")

print("\n=== 完成分析 ===")
