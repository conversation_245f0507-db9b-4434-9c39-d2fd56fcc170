"""
港交所上市公司披露报告翻译系统 - Gradio界面
"""
import os
import sys
import logging
import gradio as gr
import pandas as pd
from typing import List, Dict, Optional, Tuple
import json
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config, TRANSLATION_DIRECTIONS
from services.translation_service import TranslationService
from utils.translation_history import TranslationHistory
from utils.cost_estimator import CostEstimator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('translation_app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 验证配置
try:
    Config.validate_config()
    logger.info("配置验证成功")
except Exception as e:
    logger.error(f"配置验证失败: {e}")
    sys.exit(1)

# 初始化服务
translation_service = TranslationService()
history_manager = TranslationHistory()
cost_estimator = CostEstimator()

# 全局状态
current_progress = {"value": 0, "message": ""}
current_status = {"message": "就绪"}

def update_progress(progress: float, message: str = ""):
    """更新进度"""
    current_progress["value"] = progress
    current_progress["message"] = message

def update_status(status: str):
    """更新状态"""
    current_status["message"] = status

# 设置回调
translation_service.set_progress_callback(update_progress)
translation_service.set_status_callback(update_status)

def get_file_preview(file_path: str) -> Tuple[str, str, str, str]:
    """获取文件预览"""
    try:
        if not file_path:
            return "请先上传文件", "", "", ""
        
        preview = translation_service.get_translation_preview(file_path)
        
        if not preview.get('success', False):
            return f"预览失败: {preview.get('error', '未知错误')}", "", "", ""
        
        # 文件信息
        file_info = preview['file_info']
        file_info_text = f"""
📄 **文件信息**
- 文件名: {file_info['file_name']}
- 文件大小: {file_info['file_size_mb']:.1f} MB
- 总页数: {file_info['total_pages']}
- 内容长度: {file_info['estimated_content_length']:,} 字符
"""
        
        # 公司信息
        company_info = preview['company_info']
        company_info_text = f"""
🏢 **公司信息**
- 中文名称: {company_info.get('company_name_zh', '未识别')}
- 英文名称: {company_info.get('company_name_en', '未识别')}
- 股票代码: {company_info.get('stock_code', '未识别')}
- 报告类型: {company_info.get('report_type', '未识别')}
- 报告年份: {company_info.get('report_year', '未识别')}
"""
        
        # 成本估算
        cost_zh_en = preview['cost_estimates']['zh_to_en']
        cost_en_zh = preview['cost_estimates']['en_to_zh']
        
        cost_info_text = f"""
💰 **成本估算**

**中文→英文翻译:**
- 预估成本: ${cost_zh_en.get('estimated_cost_with_buffer', 0):.2f}
- 预估时间: {cost_zh_en.get('estimated_time_minutes', 0)} 分钟
- 每页成本: ${cost_zh_en.get('cost_per_page', 0):.3f}

**英文→中文翻译:**
- 预估成本: ${cost_en_zh.get('estimated_cost_with_buffer', 0):.2f}
- 预估时间: {cost_en_zh.get('estimated_time_minutes', 0)} 分钟
- 每页成本: ${cost_en_zh.get('cost_per_page', 0):.3f}

⚠️ 预算状态: {"正常" if not cost_zh_en.get('budget_warning', False) else "警告"}
"""
        
        # 内容预览
        sample_content = preview.get('sample_content', '')
        content_preview = f"""
📖 **内容预览** (前500字符)

{sample_content}
"""
        
        return file_info_text, company_info_text, cost_info_text, content_preview
        
    except Exception as e:
        logger.error(f"获取文件预览失败: {e}")
        return f"预览失败: {str(e)}", "", "", ""

def translate_single_file(file_path: str, translation_direction: str) -> Tuple[str, str, str]:
    """翻译单个文件"""
    try:
        if not file_path:
            return "请先上传文件", "", ""
        
        if not translation_direction:
            return "请选择翻译方向", "", ""
        
        logger.info(f"开始翻译文件: {file_path}, 方向: {translation_direction}")
        
        # 执行翻译
        result = translation_service.translate_document(file_path, translation_direction)
        
        if not result.get('success', False):
            error_msg = result.get('error', '未知错误')
            return f"翻译失败: {error_msg}", "", ""
        
        # 生成结果报告
        company_info = result['company_info']
        quality_report = result['quality_report']
        
        result_text = f"""
✅ **翻译完成!**

📊 **翻译统计**
- 项目ID: {result['project_id']}
- 公司: {company_info.get('company_name_zh', '未知')} ({company_info.get('stock_code', '未知')})
- 翻译方向: {TRANSLATION_DIRECTIONS.get(translation_direction, translation_direction)}
- 总页数: {len(result['translation_results'])}
- 处理时间: {result['processing_time']:.1f} 秒
- 实际成本: ${result['actual_cost']:.4f}

📈 **质量报告**
- 通过率: {quality_report.get('pass_rate', 0):.1%}
- 平均评分: {quality_report.get('average_score', 0):.1f}/10
- 整体质量: {quality_report.get('overall_quality', '未知')}

📁 **输出文件**: {os.path.basename(result['output_file'])}
"""
        
        return result_text, result['output_file'], result['project_id']
        
    except Exception as e:
        logger.error(f"翻译失败: {e}")
        return f"翻译失败: {str(e)}", "", ""

def translate_batch_files(file_paths: List[str], translation_direction: str) -> Tuple[str, str]:
    """批量翻译文件"""
    try:
        if not file_paths:
            return "请先上传文件", ""
        
        if not translation_direction:
            return "请选择翻译方向", ""
        
        logger.info(f"开始批量翻译 {len(file_paths)} 个文件")
        
        # 执行批量翻译
        batch_result = translation_service.batch_translate(file_paths, translation_direction)
        
        if not batch_result.get('success', True):  # 批量翻译可能部分成功
            return f"批量翻译失败: {batch_result.get('error', '未知错误')}", ""
        
        # 生成批量结果报告
        result_text = f"""
📦 **批量翻译完成!**

📊 **批量统计**
- 总文件数: {batch_result['total_files']}
- 成功文件数: {batch_result['successful_files']}
- 失败文件数: {batch_result['failed_files']}
- 成功率: {batch_result['success_rate']:.1%}
- 总成本: ${batch_result['total_cost']:.4f}

📋 **详细结果**
"""
        
        # 添加每个文件的结果
        for i, result in enumerate(batch_result['results'], 1):
            file_name = os.path.basename(result.get('input_file', f'文件{i}'))
            if result.get('success', False):
                result_text += f"\n✅ {file_name} - 成功 (${result.get('actual_cost', 0):.4f})"
            else:
                result_text += f"\n❌ {file_name} - 失败: {result.get('error', '未知错误')}"
        
        # 生成下载链接信息
        successful_files = [r for r in batch_result['results'] if r.get('success', False)]
        download_info = "\n".join([
            f"- {os.path.basename(r['output_file'])}" 
            for r in successful_files if r.get('output_file')
        ])
        
        return result_text, download_info
        
    except Exception as e:
        logger.error(f"批量翻译失败: {e}")
        return f"批量翻译失败: {str(e)}", ""

def get_translation_history() -> str:
    """获取翻译历史"""
    try:
        projects = history_manager.get_project_history(limit=20)
        
        if not projects:
            return "暂无翻译历史"
        
        history_text = "📚 **最近翻译历史**\n\n"
        
        for project in projects:
            status_emoji = "✅" if project['status'] == 'completed' else "🔄"
            company_name = project['company_name_zh'] or project['company_name_en'] or '未知公司'
            
            history_text += f"""
{status_emoji} **{company_name}** ({project['stock_code']})
- 项目ID: {project['project_id']}
- 翻译方向: {TRANSLATION_DIRECTIONS.get(project['translation_direction'], project['translation_direction'])}
- 页数: {project['total_pages']}
- 成本: ${project['total_cost'] or 0:.4f}
- 状态: {project['status']}
- 创建时间: {project['created_at'][:19]}
---
"""
        
        return history_text
        
    except Exception as e:
        logger.error(f"获取翻译历史失败: {e}")
        return f"获取历史失败: {str(e)}"

def get_cost_statistics() -> str:
    """获取成本统计"""
    try:
        stats = cost_estimator.get_cost_statistics(days=30)
        
        stats_text = f"""
💰 **成本统计** (最近30天)

📊 **总体统计**
- 总成本: ${stats.get('total_cost', 0):.2f}
- 翻译文档数: {stats.get('total_documents', 0)}
- 日均成本: ${stats.get('daily_average', 0):.2f}
- 平均每文档成本: ${stats.get('average_cost_per_document', 0):.2f}
- 活跃天数: {stats.get('days_with_usage', 0)}

📈 **使用趋势**
"""
        
        # 添加每日明细
        daily_breakdown = stats.get('daily_breakdown', {})
        for date, usage in sorted(daily_breakdown.items())[-7:]:  # 最近7天
            stats_text += f"\n- {date}: ${usage['cost']:.2f} ({usage['documents']} 文档)"
        
        return stats_text
        
    except Exception as e:
        logger.error(f"获取成本统计失败: {e}")
        return f"获取统计失败: {str(e)}"

def get_progress_info() -> Tuple[float, str]:
    """获取进度信息"""
    return current_progress["value"], current_progress["message"]

def get_status_info() -> str:
    """获取状态信息"""
    return current_status["message"]

# 创建Gradio界面
def create_interface():
    """创建Gradio界面"""
    
    with gr.Blocks(
        title="港交所上市公司披露报告翻译系统",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .tab-nav {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        """
    ) as app:
        
        gr.Markdown("""
        # 🏢 港交所上市公司披露报告翻译系统
        
        基于多智能体架构的专业翻译系统，支持中英文互译，保持术语一致性，格式完整保留。
        """)
        
        with gr.Tabs():
            # 单文档翻译标签页
            with gr.Tab("📄 单文档翻译"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### 📤 文件上传")
                        
                        single_file_input = gr.File(
                            label="上传DOCX文件",
                            file_types=[".docx"],
                            type="filepath"
                        )
                        
                        translation_direction = gr.Dropdown(
                            choices=list(TRANSLATION_DIRECTIONS.keys()),
                            label="翻译方向",
                            value="zh_to_en"
                        )
                        
                        with gr.Row():
                            preview_btn = gr.Button("📋 预览文档", variant="secondary")
                            translate_btn = gr.Button("🚀 开始翻译", variant="primary")
                    
                    with gr.Column(scale=2):
                        gr.Markdown("### 📊 文档信息")
                        
                        with gr.Tabs():
                            with gr.Tab("文件信息"):
                                file_info_display = gr.Markdown()
                            
                            with gr.Tab("公司信息"):
                                company_info_display = gr.Markdown()
                            
                            with gr.Tab("成本估算"):
                                cost_info_display = gr.Markdown()
                            
                            with gr.Tab("内容预览"):
                                content_preview_display = gr.Markdown()
                
                gr.Markdown("### 📈 翻译进度")
                
                with gr.Row():
                    progress_bar = gr.Progress()
                    status_display = gr.Textbox(
                        label="状态",
                        value="就绪",
                        interactive=False
                    )
                
                gr.Markdown("### 📋 翻译结果")
                
                with gr.Row():
                    with gr.Column(scale=2):
                        translation_result = gr.Markdown()
                    
                    with gr.Column(scale=1):
                        output_file = gr.File(
                            label="下载翻译结果",
                            interactive=False
                        )
                        
                        project_id_display = gr.Textbox(
                            label="项目ID",
                            interactive=False
                        )
                
                # 事件绑定
                preview_btn.click(
                    fn=get_file_preview,
                    inputs=[single_file_input],
                    outputs=[file_info_display, company_info_display, cost_info_display, content_preview_display]
                )
                
                translate_btn.click(
                    fn=translate_single_file,
                    inputs=[single_file_input, translation_direction],
                    outputs=[translation_result, output_file, project_id_display]
                )
            
            # 批量翻译标签页
            with gr.Tab("📦 批量翻译"):
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### 📤 批量文件上传")
                        
                        batch_files_input = gr.File(
                            label="上传多个DOCX文件",
                            file_types=[".docx"],
                            file_count="multiple",
                            type="filepath"
                        )
                        
                        batch_translation_direction = gr.Dropdown(
                            choices=list(TRANSLATION_DIRECTIONS.keys()),
                            label="翻译方向",
                            value="zh_to_en"
                        )
                        
                        batch_translate_btn = gr.Button("🚀 开始批量翻译", variant="primary")
                    
                    with gr.Column():
                        gr.Markdown("### 📊 批量翻译结果")
                        
                        batch_result_display = gr.Markdown()
                        
                        gr.Markdown("### 📁 下载文件")
                        batch_download_info = gr.Markdown()
                
                # 事件绑定
                batch_translate_btn.click(
                    fn=translate_batch_files,
                    inputs=[batch_files_input, batch_translation_direction],
                    outputs=[batch_result_display, batch_download_info]
                )
            
            # 翻译历史标签页
            with gr.Tab("📚 翻译历史"):
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### 📋 历史记录")
                        
                        refresh_history_btn = gr.Button("🔄 刷新历史", variant="secondary")
                        history_display = gr.Markdown()
                    
                    with gr.Column():
                        gr.Markdown("### 💰 成本统计")
                        
                        refresh_stats_btn = gr.Button("🔄 刷新统计", variant="secondary")
                        stats_display = gr.Markdown()
                
                # 事件绑定
                refresh_history_btn.click(
                    fn=get_translation_history,
                    outputs=[history_display]
                )
                
                refresh_stats_btn.click(
                    fn=get_cost_statistics,
                    outputs=[stats_display]
                )
                
                # 页面加载时自动刷新
                app.load(
                    fn=get_translation_history,
                    outputs=[history_display]
                )
                
                app.load(
                    fn=get_cost_statistics,
                    outputs=[stats_display]
                )
        
        gr.Markdown("""
        ---
        💡 **使用提示:**
        - 支持的文件格式: DOCX
        - 最大文件大小: 50MB
        - 最大页数: 200页
        - 翻译质量通过多智能体协作保证
        - 自动参考历史翻译保持术语一致性
        """)
    
    return app

if __name__ == "__main__":
    # 创建并启动应用
    app = create_interface()
    
    # 启动服务器
    app.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=False,
        show_error=True,
        max_file_size=f"{Config.MAX_FILE_SIZE_MB}mb"
    )
