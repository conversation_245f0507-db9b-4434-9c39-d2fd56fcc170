"""
强化的JSON解析工具 - 处理AI模型返回的不规范JSON
"""
import json
import re
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class RobustJSONParser:
    """强化的JSON解析器"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本中的控制字符和不可见字符"""
        if not text:
            return ""
        
        # 移除控制字符，但保留换行符、制表符和回车符
        cleaned = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # 移除零宽字符
        cleaned = re.sub(r'[\u200B-\u200D\uFEFF]', '', cleaned)
        
        # 标准化引号
        cleaned = cleaned.replace('"', '"').replace('"', '"')
        cleaned = cleaned.replace(''', "'").replace(''', "'")
        
        return cleaned
    
    @staticmethod
    def extract_json_object(text: str) -> Optional[str]:
        """从文本中提取第一个完整的JSON对象"""
        try:
            # 查找第一个 {
            start = text.find('{')
            if start == -1:
                return None
            
            # 计算括号匹配
            brace_count = 0
            in_string = False
            escape_next = False
            
            for i, char in enumerate(text[start:], start):
                if escape_next:
                    escape_next = False
                    continue
                
                if char == '\\':
                    escape_next = True
                    continue
                
                if char == '"' and not escape_next:
                    in_string = not in_string
                    continue
                
                if not in_string:
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            return text[start:i+1]
            
            return None
            
        except Exception as e:
            logger.warning(f"提取JSON对象失败: {e}")
            return None
    
    @staticmethod
    def fix_common_json_issues(json_str: str) -> str:
        """修复常见的JSON格式问题"""
        try:
            # 移除JSON前后的多余文本
            json_str = json_str.strip()
            
            # 修复常见的引号问题
            json_str = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', json_str)
            
            # 修复布尔值
            json_str = re.sub(r'\btrue\b', 'true', json_str, flags=re.IGNORECASE)
            json_str = re.sub(r'\bfalse\b', 'false', json_str, flags=re.IGNORECASE)
            json_str = re.sub(r'\bnull\b', 'null', json_str, flags=re.IGNORECASE)
            
            # 修复数字格式
            json_str = re.sub(r':\s*([0-9]+\.?[0-9]*)\s*([,}])', r': \1\2', json_str)
            
            # 移除尾随逗号
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)
            
            return json_str
            
        except Exception as e:
            logger.warning(f"修复JSON格式失败: {e}")
            return json_str
    
    @classmethod
    def parse(cls, text: str, default: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        强化的JSON解析
        
        Args:
            text: 要解析的文本
            default: 解析失败时的默认值
            
        Returns:
            解析后的字典
        """
        if default is None:
            default = {}
        
        if not text or not text.strip():
            return default
        
        try:
            # 1. 清理文本
            cleaned_text = cls.clean_text(text)
            
            # 2. 直接尝试解析
            try:
                return json.loads(cleaned_text)
            except json.JSONDecodeError:
                pass
            
            # 3. 提取JSON对象
            json_obj = cls.extract_json_object(cleaned_text)
            if json_obj:
                try:
                    return json.loads(json_obj)
                except json.JSONDecodeError:
                    # 4. 修复常见问题后再试
                    fixed_json = cls.fix_common_json_issues(json_obj)
                    try:
                        return json.loads(fixed_json)
                    except json.JSONDecodeError:
                        pass
            
            # 5. 尝试逐行解析关键信息
            return cls.parse_key_value_pairs(cleaned_text, default)
            
        except Exception as e:
            logger.error(f"JSON解析完全失败: {e}")
            return default
    
    @staticmethod
    def parse_key_value_pairs(text: str, default: Dict[str, Any]) -> Dict[str, Any]:
        """从文本中解析键值对"""
        result = default.copy()
        
        try:
            lines = text.split('\n')
            
            for line in lines:
                line = line.strip()
                
                # 匹配各种键值对格式
                patterns = [
                    r'"([^"]+)"\s*:\s*"([^"]*)"',  # "key": "value"
                    r'"([^"]+)"\s*:\s*([0-9]+\.?[0-9]*)',  # "key": number
                    r'"([^"]+)"\s*:\s*(true|false)',  # "key": boolean
                    r'([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*"([^"]*)"',  # key: "value"
                    r'([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*([0-9]+\.?[0-9]*)',  # key: number
                    r'([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*(true|false)',  # key: boolean
                ]
                
                for pattern in patterns:
                    match = re.search(pattern, line, re.IGNORECASE)
                    if match:
                        key = match.group(1)
                        value = match.group(2)
                        
                        # 转换值类型
                        if value.lower() == 'true':
                            result[key] = True
                        elif value.lower() == 'false':
                            result[key] = False
                        elif value.replace('.', '').isdigit():
                            result[key] = float(value) if '.' in value else int(value)
                        else:
                            result[key] = value
                        break
                
                # 特殊处理数组
                array_match = re.search(r'"([^"]+)"\s*:\s*\[(.*?)\]', line)
                if array_match:
                    key = array_match.group(1)
                    array_content = array_match.group(2)
                    
                    # 简单的数组解析
                    items = []
                    for item in array_content.split(','):
                        item = item.strip().strip('"')
                        if item:
                            items.append(item)
                    result[key] = items
            
            return result
            
        except Exception as e:
            logger.warning(f"键值对解析失败: {e}")
            return default

def parse_ai_response(response_text: str, expected_keys: list = None, default_values: dict = None) -> Dict[str, Any]:
    """
    解析AI模型的响应
    
    Args:
        response_text: AI响应文本
        expected_keys: 期望的键列表
        default_values: 默认值字典
        
    Returns:
        解析后的字典
    """
    if default_values is None:
        default_values = {}
    
    if expected_keys is None:
        expected_keys = []
    
    # 使用强化解析器
    result = RobustJSONParser.parse(response_text, default_values)
    
    # 确保包含所有期望的键
    for key in expected_keys:
        if key not in result:
            if key in default_values:
                result[key] = default_values[key]
            else:
                # 根据键名推断默认值
                if 'score' in key.lower():
                    result[key] = 5
                elif 'needs_revision' in key.lower():
                    result[key] = True
                elif key.lower() in ['issues', 'suggestions']:
                    result[key] = []
                else:
                    result[key] = ""
    
    return result

# 便捷函数
def parse_review_response(response_text: str) -> Dict[str, Any]:
    """解析校对响应"""
    expected_keys = [
        'score', 'accuracy_score', 'consistency_score', 'terminology_score',
        'issues', 'suggestions', 'reference_usage', 'needs_revision', 'feedback'
    ]
    
    default_values = {
        'score': 5,
        'accuracy_score': 5,
        'consistency_score': 5,
        'terminology_score': 5,
        'issues': [],
        'suggestions': [],
        'reference_usage': '',
        'needs_revision': True,
        'feedback': response_text
    }
    
    return parse_ai_response(response_text, expected_keys, default_values)

def parse_translation_response(response_text: str) -> Dict[str, Any]:
    """解析翻译响应"""
    expected_keys = ['translated_text', 'notes']
    
    default_values = {
        'translated_text': response_text,
        'notes': ''
    }
    
    return parse_ai_response(response_text, expected_keys, default_values)
