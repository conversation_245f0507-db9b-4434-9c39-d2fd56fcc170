2025-05-26 21:16:45,300 - app - INFO - 配置验证成功
2025-05-26 21:16:45,302 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-05-26 21:16:45,302 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-26 21:16:55,443 - sentence_transformers.SentenceTransformer - WARNING - No sentence-transformers model found with name sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2. Creating a new one with mean pooling.
2025-05-26 21:18:18,786 - app - INFO - 配置验证成功
2025-05-26 21:18:18,788 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-05-26 21:18:18,788 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-26 21:18:25,600 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-05-26 21:19:15,532 - huggingface_hub.file_download - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-05-26 21:19:21,523 - utils.knowledge_base - INFO - 已创建新集合: hkex_reports
2025-05-26 21:19:31,100 - utils.translation_history - INFO - 翻译历史数据库初始化完成
2025-05-26 21:19:32,491 - utils.translation_history - INFO - 翻译历史数据库初始化完成
2025-05-26 21:19:35,312 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-05-26 21:19:35,686 - httpx - INFO - HTTP Request: GET http://localhost:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-05-26 21:19:37,755 - httpx - INFO - HTTP Request: HEAD http://localhost:7860/ "HTTP/1.1 200 OK"
2025-05-26 21:21:15,905 - utils.document_processor - INFO - 提取的公司信息: {'company_name_zh': '致英恒科技控股有限公司股東', 'company_name_en': '', 'stock_code': '2846', 'report_type': '', 'report_year': '2021'}
2025-05-26 21:21:16,017 - utils.document_processor - INFO - 文档分割完成，共 3 页
2025-05-26 21:21:16,023 - utils.cost_estimator - INFO - 文档成本估算完成：3 页，预估成本 $0.26
2025-05-26 21:21:16,025 - utils.cost_estimator - INFO - 文档成本估算完成：3 页，预估成本 $0.26
2025-05-26 21:22:18,716 - utils.document_processor - INFO - 提取的公司信息: {'company_name_zh': '', 'company_name_en': '', 'stock_code': '2846', 'report_type': 'annual_report', 'report_year': '2021'}
2025-05-26 21:22:19,097 - utils.document_processor - INFO - 文档分割完成，共 3 页
2025-05-26 21:22:19,100 - utils.cost_estimator - INFO - 文档成本估算完成：3 页，预估成本 $0.17
2025-05-26 21:22:19,103 - utils.cost_estimator - INFO - 文档成本估算完成：3 页，预估成本 $0.17
2025-05-26 21:22:45,364 - app - INFO - 开始翻译文件: C:\Users\<USER>\AppData\Local\Temp\gradio\fa9788fc8500fdc914300e4fa920ca63da5f661f19301c84a45a5d4a41244afc\ForTest2021_ar_e.docx, 方向: en_to_zh
2025-05-26 21:22:45,407 - utils.document_processor - INFO - 提取的公司信息: {'company_name_zh': '', 'company_name_en': '', 'stock_code': '2846', 'report_type': 'annual_report', 'report_year': '2021'}
2025-05-26 21:22:45,766 - utils.document_processor - INFO - 文档分割完成，共 3 页
2025-05-26 21:22:45,769 - utils.cost_estimator - INFO - 文档成本估算完成：3 页，预估成本 $0.17
2025-05-26 21:22:45,775 - utils.translation_history - INFO - 创建翻译项目: 1440189f471b4b39
2025-05-26 21:23:00,164 - utils.hkex_scraper - INFO - Chrome WebDriver 初始化成功
2025-05-26 21:23:25,154 - utils.hkex_scraper - WARNING - 股票代码搜索失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff7685e91f5+2853845]
	GetHandleVerifier [0x0x7ff768343ac0+79008]
	(No symbol) [0x0x7ff768109bda]
	(No symbol) [0x0x7ff7681600f6]
	(No symbol) [0x0x7ff7681603ac]
	(No symbol) [0x0x7ff7681b3b07]
	(No symbol) [0x0x7ff7681884ff]
	(No symbol) [0x0x7ff7681b08f5]
	(No symbol) [0x0x7ff768188293]
	(No symbol) [0x0x7ff768151061]
	(No symbol) [0x0x7ff768151df3]
	GetHandleVerifier [0x0x7ff76861410d+3029741]
	GetHandleVerifier [0x0x7ff76860e52d+3006221]
	GetHandleVerifier [0x0x7ff76862d5b2+3133330]
	GetHandleVerifier [0x0x7ff76835d98e+185198]
	GetHandleVerifier [0x0x7ff768364edf+215231]
	GetHandleVerifier [0x0x7ff76834c324+113924]
	GetHandleVerifier [0x0x7ff76834c4d9+114361]
	GetHandleVerifier [0x0x7ff768333208+11240]
	BaseThreadInitThunk [0x0x7ffe03fee8d7+23]
	RtlUserThreadStart [0x0x7ffe0561fbcc+44]

2025-05-26 21:23:25,155 - utils.hkex_scraper - WARNING - 未找到公司  (2846) 的报告
2025-05-26 21:23:27,297 - services.translation_service - WARNING - 未能获取参考报告，将使用现有知识库
2025-05-26 21:23:27,383 - utils.knowledge_base - ERROR - 搜索相似内容失败: Expected where to have exactly one operator, got {'language': 'zh', 'stock_code': '2846'} in query.
2025-05-26 21:23:27,384 - agents.translation_agent - INFO - 开始翻译第 1 页，预估成本: $0.0189
2025-05-26 21:23:30,208 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-26 21:23:49,872 - agents.translation_agent - WARNING - 解析翻译响应失败: Invalid control character at: line 2 column 27 (char 28)
2025-05-26 21:23:49,873 - agents.translation_agent - INFO - 第 1 页翻译完成，实际成本: $0.0274
2025-05-26 21:23:49,873 - agents.review_agent - INFO - 开始校对第 1 页
2025-05-26 21:23:53,160 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-26 21:24:06,512 - agents.review_agent - WARNING - 解析校对响应失败: Expecting ',' delimiter: line 11 column 23 (char 234)
2025-05-26 21:24:06,513 - agents.review_agent - WARNING - 第 1 页需要修订，评分: 5
2025-05-26 21:24:09,107 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-26 21:24:37,799 - agents.translation_agent - WARNING - 解析翻译响应失败: Invalid control character at: line 2 column 31 (char 32)
2025-05-26 21:24:37,799 - services.translation_service - INFO - 第 1 页第 1 次修订完成
2025-05-26 21:24:37,800 - agents.review_agent - INFO - 开始校对第 1 页
2025-05-26 21:24:40,399 - httpx - INFO - HTTP Request: POST https://openrouter.ai/api/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-26 21:24:51,349 - agents.review_agent - WARNING - 解析校对响应失败: Invalid control character at: line 17 column 34 (char 444)
2025-05-26 21:24:51,349 - agents.review_agent - WARNING - 第 1 页需要修订，评分: 5
2025-05-26 21:25:06,387 - openai._base_client - INFO - Retrying request to /chat/completions in 0.482586 seconds
2025-05-26 21:25:21,907 - openai._base_client - INFO - Retrying request to /chat/completions in 0.768292 seconds
2025-05-26 21:25:37,714 - agents.translation_agent - ERROR - 翻译修订失败: Request timed out.
2025-05-26 21:25:37,714 - services.translation_service - INFO - 第 1 页第 2 次修订完成
2025-05-26 21:25:37,715 - agents.review_agent - INFO - 开始校对第 1 页
2025-05-26 21:25:52,747 - openai._base_client - INFO - Retrying request to /chat/completions in 0.462924 seconds
2025-05-26 21:26:08,241 - openai._base_client - INFO - Retrying request to /chat/completions in 0.838103 seconds
2025-05-26 21:26:24,111 - agents.review_agent - ERROR - 校对失败: Request timed out.
2025-05-26 21:26:39,159 - openai._base_client - INFO - Retrying request to /chat/completions in 0.390875 seconds
2025-05-26 21:30:39,478 - app - INFO - 配置验证成功
2025-05-26 21:30:39,480 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-05-26 21:30:39,480 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-26 21:33:15,842 - app - INFO - 配置验证成功
2025-05-26 21:33:15,844 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-05-26 21:33:15,844 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-26 21:33:22,458 - utils.knowledge_base - INFO - 已连接到现有集合: hkex_reports
2025-05-26 21:33:22,583 - utils.translation_history - INFO - 翻译历史数据库初始化完成
2025-05-26 21:33:24,045 - utils.translation_history - INFO - 翻译历史数据库初始化完成
2025-05-26 21:33:27,047 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-05-26 21:33:27,157 - httpx - INFO - HTTP Request: GET http://localhost:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
2025-05-26 21:33:29,205 - httpx - INFO - HTTP Request: HEAD http://localhost:7860/ "HTTP/1.1 200 OK"
2025-05-26 21:34:05,421 - utils.document_processor - INFO - 提取的公司信息: {'company_name_zh': '', 'company_name_en': '', 'stock_code': '2022', 'report_type': 'annual_report', 'report_year': '2022'}
2025-05-26 21:34:10,506 - utils.document_processor - INFO - 文档分割完成，共 45 页
2025-05-26 21:34:10,546 - utils.cost_estimator - INFO - 文档成本估算完成：45 页，预估成本 $2.97
2025-05-26 21:34:10,583 - utils.cost_estimator - INFO - 文档成本估算完成：45 页，预估成本 $2.97
2025-05-26 21:44:55,713 - app - INFO - 配置验证成功
2025-05-26 21:44:55,715 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-05-26 21:44:55,715 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: paraphrase-multilingual-MiniLM-L12-v2
2025-05-26 21:45:02,560 - utils.knowledge_base - INFO - 已连接到现有集合: hkex_reports
2025-05-26 21:45:02,691 - utils.translation_history - INFO - 翻译历史数据库初始化完成
2025-05-26 21:45:04,121 - utils.translation_history - INFO - 翻译历史数据库初始化完成
2025-05-26 21:45:06,913 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-05-26 21:45:07,243 - httpx - INFO - HTTP Request: GET http://localhost:7860/gradio_api/startup-events "HTTP/1.1 200 OK"
