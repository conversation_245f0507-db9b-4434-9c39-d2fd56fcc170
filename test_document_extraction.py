#!/usr/bin/env python3
"""
测试文档信息提取功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_documents():
    """创建多个测试文档来验证提取功能"""
    from docx import Document
    
    test_docs = []
    
    # 测试文档1：腾讯控股
    print("📄 创建腾讯控股测试文档...")
    doc1 = Document()
    doc1.add_heading('腾讯控股有限公司', 0)
    doc1.add_heading('Tencent Holdings Limited', 1)
    doc1.add_heading('2023年年度报告', 1)
    doc1.add_paragraph('股票代码：0700')
    doc1.add_paragraph('Stock Code: 0700')
    doc1.add_paragraph('公司名称：腾讯控股有限公司')
    doc1.add_paragraph('Company Name: Tencent Holdings Limited')
    doc1.add_paragraph('报告类型：年度报告')
    doc1.add_paragraph('Report Type: Annual Report')
    doc1.add_paragraph('报告年份：2023年')
    
    # 添加表格
    table1 = doc1.add_table(rows=3, cols=2)
    table1.cell(0, 0).text = '项目'
    table1.cell(0, 1).text = '内容'
    table1.cell(1, 0).text = '股票代码'
    table1.cell(1, 1).text = '0700'
    table1.cell(2, 0).text = '报告年度'
    table1.cell(2, 1).text = '2023'
    
    file1 = "test_tencent_0700.docx"
    doc1.save(file1)
    test_docs.append(file1)
    
    # 测试文档2：阿里巴巴
    print("📄 创建阿里巴巴测试文档...")
    doc2 = Document()
    doc2.add_heading('阿里巴巴集团控股有限公司', 0)
    doc2.add_heading('Alibaba Group Holding Limited', 1)
    doc2.add_heading('2023年中期报告', 1)
    doc2.add_paragraph('港股代码：9988')
    doc2.add_paragraph('HK Stock Code: 9988')
    doc2.add_paragraph('公司全称：阿里巴巴集团控股有限公司')
    doc2.add_paragraph('Full Company Name: Alibaba Group Holding Limited')
    doc2.add_paragraph('本报告为中期报告')
    doc2.add_paragraph('This is an Interim Report')
    doc2.add_paragraph('截至2023年9月30日止六个月')
    
    file2 = "test_alibaba_9988.docx"
    doc2.save(file2)
    test_docs.append(file2)
    
    # 测试文档3：小米集团
    print("📄 创建小米集团测试文档...")
    doc3 = Document()
    doc3.add_heading('小米集团', 0)
    doc3.add_heading('Xiaomi Corporation', 1)
    doc3.add_heading('2023年第三季度业绩报告', 1)
    doc3.add_paragraph('证券代码：1810')
    doc3.add_paragraph('Securities Code: 1810')
    doc3.add_paragraph('公司名称：小米集团')
    doc3.add_paragraph('Company: Xiaomi Corporation')
    doc3.add_paragraph('季度报告 - 第三季度')
    doc3.add_paragraph('Quarterly Report - Q3')
    doc3.add_paragraph('2023年第三季度业绩')
    
    file3 = "test_xiaomi_1810.docx"
    doc3.save(file3)
    test_docs.append(file3)
    
    # 测试文档4：复杂格式
    print("📄 创建复杂格式测试文档...")
    doc4 = Document()
    doc4.add_heading('中国移动有限公司 China Mobile Limited', 0)
    doc4.add_paragraph('股份代号: 0941 | Stock Code: 0941')
    doc4.add_paragraph('二零二三年度全年業績公告')
    doc4.add_paragraph('Annual Results Announcement for the Year 2023')
    doc4.add_paragraph('中国移动有限公司（「本公司」）')
    doc4.add_paragraph('China Mobile Limited (the "Company")')
    
    # 添加复杂表格
    table4 = doc4.add_table(rows=4, cols=3)
    table4.cell(0, 0).text = '项目 Item'
    table4.cell(0, 1).text = '中文 Chinese'
    table4.cell(0, 2).text = '英文 English'
    table4.cell(1, 0).text = '公司名称'
    table4.cell(1, 1).text = '中国移动有限公司'
    table4.cell(1, 2).text = 'China Mobile Limited'
    table4.cell(2, 0).text = '股票代码'
    table4.cell(2, 1).text = '0941'
    table4.cell(2, 2).text = '0941'
    table4.cell(3, 0).text = '年度'
    table4.cell(3, 1).text = '2023年'
    table4.cell(3, 2).text = 'Year 2023'
    
    file4 = "test_china_mobile_0941.docx"
    doc4.save(file4)
    test_docs.append(file4)
    
    return test_docs

def test_extraction(file_path: str):
    """测试单个文档的信息提取"""
    from utils.document_processor import DocumentProcessor
    
    print(f"\n🔍 测试文档: {file_path}")
    print("-" * 50)
    
    processor = DocumentProcessor()
    company_info = processor.extract_company_info(file_path)
    
    print(f"📊 提取结果:")
    print(f"   中文名称: {company_info.get('company_name_zh', '未识别')}")
    print(f"   英文名称: {company_info.get('company_name_en', '未识别')}")
    print(f"   股票代码: {company_info.get('stock_code', '未识别')}")
    print(f"   报告类型: {company_info.get('report_type', '未识别')}")
    print(f"   报告年份: {company_info.get('report_year', '未识别')}")
    
    # 评估提取质量
    score = 0
    total = 5
    
    if company_info.get('company_name_zh'):
        score += 1
        print("   ✅ 中文名称提取成功")
    else:
        print("   ❌ 中文名称提取失败")
    
    if company_info.get('company_name_en'):
        score += 1
        print("   ✅ 英文名称提取成功")
    else:
        print("   ❌ 英文名称提取失败")
    
    if company_info.get('stock_code') and company_info.get('stock_code') != '2023':
        score += 1
        print("   ✅ 股票代码提取成功")
    else:
        print("   ❌ 股票代码提取失败或错误")
    
    if company_info.get('report_type'):
        score += 1
        print("   ✅ 报告类型提取成功")
    else:
        print("   ❌ 报告类型提取失败")
    
    if company_info.get('report_year'):
        score += 1
        print("   ✅ 报告年份提取成功")
    else:
        print("   ❌ 报告年份提取失败")
    
    print(f"   📈 提取质量: {score}/{total} ({score/total*100:.1f}%)")
    
    return score, total

def main():
    """主测试函数"""
    print("🧪 文档信息提取功能测试")
    print("=" * 60)
    
    try:
        # 创建测试文档
        test_docs = create_test_documents()
        
        total_score = 0
        total_possible = 0
        
        # 测试每个文档
        for doc_file in test_docs:
            try:
                score, possible = test_extraction(doc_file)
                total_score += score
                total_possible += possible
            except Exception as e:
                print(f"❌ 测试文档 {doc_file} 失败: {e}")
        
        # 总结结果
        print("\n" + "=" * 60)
        print(f"📊 总体测试结果:")
        print(f"   总体提取成功率: {total_score}/{total_possible} ({total_score/total_possible*100:.1f}%)")
        
        if total_score / total_possible >= 0.8:
            print("🎉 提取功能表现优秀！")
        elif total_score / total_possible >= 0.6:
            print("✅ 提取功能表现良好")
        else:
            print("⚠️  提取功能需要改进")
        
        # 清理测试文件
        print("\n🧹 清理测试文件...")
        for doc_file in test_docs:
            if os.path.exists(doc_file):
                os.remove(doc_file)
                print(f"   已删除: {doc_file}")
        
        return total_score / total_possible >= 0.6
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
