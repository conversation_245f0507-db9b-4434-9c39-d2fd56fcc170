#!/usr/bin/env python3
"""
测试简化的参考翻译系统
"""
import os
import sys
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.reference_processor import ReferenceProcessor
from utils.simple_knowledge_base import SimpleKnowledgeBase
from services.reference_translation_service import ReferenceTranslationService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_reference_processor():
    """测试参考文档处理器"""
    print("=" * 60)
    print("测试参考文档处理器")
    print("=" * 60)
    
    processor = ReferenceProcessor()
    
    # 测试文档验证
    test_file = "2022_ar_e.docx"
    if os.path.exists(test_file):
        print(f"📄 测试文档: {test_file}")
        
        # 验证文档
        is_valid, error_msg = processor.validate_document(test_file)
        print(f"文档验证: {'✅ 有效' if is_valid else f'❌ 无效 - {error_msg}'}")
        
        if is_valid:
            # 提取基本信息
            basic_info = processor.extract_basic_info(test_file)
            print(f"基本信息:")
            for key, value in basic_info.items():
                print(f"  {key}: {value}")
            
            # 分割文档
            pages = processor.split_document_by_pages(test_file)
            print(f"文档分割: 共 {len(pages)} 页")
            
            if pages:
                first_page = pages[0]
                print(f"第一页信息:")
                print(f"  页码: {first_page.get('page_number', '未知')}")
                print(f"  段落数: {len(first_page.get('paragraphs', []))}")
                print(f"  表格数: {len(first_page.get('tables', []))}")
                print(f"  内容预览: {first_page.get('content', '')[:200]}...")
    else:
        print(f"❌ 测试文档不存在: {test_file}")

def test_simple_knowledge_base():
    """测试简化知识库"""
    print("\n" + "=" * 60)
    print("测试简化知识库")
    print("=" * 60)
    
    kb = SimpleKnowledgeBase("test_kb")
    
    # 获取统计信息
    stats = kb.get_reference_statistics()
    print(f"📊 知识库统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    # 列出参考文档
    references = kb.list_references()
    print(f"📚 参考文档数量: {len(references)}")
    
    for ref in references[:3]:  # 只显示前3个
        print(f"  - {ref.get('reference_name', '未知')}")
        print(f"    中文: {ref.get('zh_count', 0)}, 英文: {ref.get('en_count', 0)}, 术语: {ref.get('term_count', 0)}")
    
    # 测试搜索功能
    if stats.get('total_documents', 0) > 0:
        print("\n🔍 测试搜索功能:")
        
        # 搜索相似内容
        test_queries = [
            "公司业务",
            "financial performance", 
            "董事会",
            "annual report"
        ]
        
        for query in test_queries:
            target_lang = 'en' if any(c.isascii() and c.isalpha() for c in query) else 'zh'
            results = kb.search_similar_content(query, target_lang, top_k=2)
            
            print(f"  查询: '{query}' (目标语言: {target_lang})")
            print(f"  结果数量: {len(results)}")
            
            for i, result in enumerate(results, 1):
                similarity = result.get('similarity_score', 0)
                content = result.get('content', '')[:100]
                print(f"    {i}. 相似度: {similarity:.3f} - {content}...")
        
        # 测试术语搜索
        print("\n🔤 测试术语搜索:")
        term_queries = ["董事会", "annual report", "股东", "revenue"]
        
        for term in term_queries:
            source_lang = 'zh' if any('\u4e00' <= c <= '\u9fff' for c in term) else 'en'
            term_results = kb.search_terminology(term, source_lang)
            
            print(f"  术语: '{term}' (源语言: {source_lang})")
            print(f"  找到 {len(term_results)} 个术语翻译")
            
            for result in term_results[:2]:  # 只显示前2个
                zh_term = result.get('zh_term', '')
                en_term = result.get('en_term', '')
                similarity = result.get('similarity_score', 0)
                print(f"    {zh_term} ↔ {en_term} (相似度: {similarity:.3f})")
    else:
        print("⚠️ 知识库为空，跳过搜索测试")

def test_translation_service():
    """测试翻译服务"""
    print("\n" + "=" * 60)
    print("测试翻译服务")
    print("=" * 60)
    
    service = ReferenceTranslationService()
    
    # 测试预览功能
    test_file = "2022_ar_e.docx"
    if os.path.exists(test_file):
        print(f"📄 测试文档预览: {test_file}")
        
        preview = service.get_translation_preview(test_file)
        
        if preview.get('success', False):
            file_info = preview.get('file_info', {})
            print(f"  文件名: {file_info.get('file_name', '未知')}")
            print(f"  文件大小: {file_info.get('file_size_mb', 0):.2f} MB")
            print(f"  总页数: {file_info.get('total_pages', 0)}")
            print(f"  检测语言: {file_info.get('detected_language', '未知')}")
            
            cost_estimates = preview.get('cost_estimates', {})
            zh_en_cost = cost_estimates.get('zh_to_en', {}).get('estimated_cost', 0)
            en_zh_cost = cost_estimates.get('en_to_zh', {}).get('estimated_cost', 0)
            print(f"  中译英成本: ${zh_en_cost:.4f}")
            print(f"  英译中成本: ${en_zh_cost:.4f}")
        else:
            print(f"❌ 预览失败: {preview.get('error', '未知错误')}")
    else:
        print(f"❌ 测试文档不存在: {test_file}")
    
    # 测试知识库信息
    kb_info = service.get_knowledge_base_info()
    if kb_info.get('success', False):
        stats = kb_info.get('statistics', {})
        references = kb_info.get('references', [])
        
        print(f"\n📊 知识库信息:")
        print(f"  参考文档数: {stats.get('reference_count', 0)}")
        print(f"  中文文档片段: {stats.get('chinese_documents', 0)}")
        print(f"  英文文档片段: {stats.get('english_documents', 0)}")
        print(f"  术语对照: {stats.get('terminology_pairs', 0)}")
        print(f"  参考文档列表: {len(references)} 个")

def create_sample_documents():
    """创建示例文档用于测试"""
    print("\n" + "=" * 60)
    print("创建示例文档")
    print("=" * 60)
    
    try:
        from docx import Document
        
        # 创建中文示例文档
        zh_doc = Document()
        zh_doc.add_heading('公司年度报告', 0)
        zh_doc.add_paragraph('本公司是一家专业从事技术开发的企业。')
        zh_doc.add_paragraph('董事会负责公司的重大决策。')
        zh_doc.add_paragraph('本年度公司业绩表现良好，营业收入增长显著。')
        
        # 添加表格
        table = zh_doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = '项目'
        table.cell(0, 1).text = '金额'
        table.cell(1, 0).text = '营业收入'
        table.cell(1, 1).text = '1000万元'
        
        zh_doc.save('sample_zh.docx')
        print("✅ 创建中文示例文档: sample_zh.docx")
        
        # 创建英文示例文档
        en_doc = Document()
        en_doc.add_heading('Annual Report', 0)
        en_doc.add_paragraph('The Company is a professional enterprise engaged in technology development.')
        en_doc.add_paragraph('The Board of Directors is responsible for major decisions of the company.')
        en_doc.add_paragraph('The company performed well this year with significant growth in operating revenue.')
        
        # 添加表格
        table = en_doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = 'Item'
        table.cell(0, 1).text = 'Amount'
        table.cell(1, 0).text = 'Operating Revenue'
        table.cell(1, 1).text = '10 million yuan'
        
        en_doc.save('sample_en.docx')
        print("✅ 创建英文示例文档: sample_en.docx")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例文档失败: {e}")
        return False

def test_add_sample_reference():
    """测试添加示例参考文档"""
    print("\n" + "=" * 60)
    print("测试添加示例参考文档")
    print("=" * 60)
    
    service = ReferenceTranslationService()
    
    # 检查是否有可用的测试文档
    test_files = [
        ("sample_zh.docx", "sample_en.docx"),
        ("2022_ar_c.docx", "2022_ar_e.docx"),  # 假设的中英文对
    ]
    
    available_pairs = []
    for zh_file, en_file in test_files:
        if os.path.exists(zh_file) and os.path.exists(en_file):
            available_pairs.append((zh_file, en_file))
    
    if not available_pairs:
        print("⚠️ 没有找到可用的测试文档对，尝试创建示例文档...")
        if create_sample_documents():
            available_pairs = [("sample_zh.docx", "sample_en.docx")]
    
    if available_pairs:
        print(f"📚 找到 {len(available_pairs)} 个可用的文档对")
        
        # 添加第一个文档对作为测试
        zh_path, en_path = available_pairs[0]
        print(f"添加测试文档对: {zh_path} + {en_path}")
        
        result = service.add_reference_documents([(zh_path, en_path)])
        
        if result.get('success', False):
            successful = result.get('successful_pairs', 0)
            total = result.get('total_pairs', 0)
            print(f"✅ 成功添加 {successful}/{total} 个参考文档对")
            
            stats = result.get('knowledge_base_stats', {})
            print(f"📊 更新后的知识库统计:")
            for key, value in stats.items():
                print(f"  {key}: {value}")
        else:
            print(f"❌ 添加失败: {result.get('error', '未知错误')}")
    else:
        print("⚠️ 没有找到可用的测试文档对")

def main():
    """主测试函数"""
    print("🧪 简化参考翻译系统测试")
    print("=" * 60)
    
    try:
        # 1. 测试参考文档处理器
        test_reference_processor()
        
        # 2. 测试简化知识库
        test_simple_knowledge_base()
        
        # 3. 测试翻译服务
        test_translation_service()
        
        # 4. 测试添加参考文档
        test_add_sample_reference()
        
        # 5. 再次测试知识库（查看添加后的状态）
        print("\n" + "=" * 60)
        print("测试添加文档后的知识库状态")
        print("=" * 60)
        test_simple_knowledge_base()
        
        print("\n" + "=" * 60)
        print("✅ 测试完成")
        print("=" * 60)
        
        print("\n📋 使用说明:")
        print("1. 系统已重构为基于参考文档的翻译模式")
        print("2. 不再依赖外部模型，使用简化的本地匹配算法")
        print("3. 运行 python reference_app.py 启动Gradio界面")
        print("4. 在'参考文档管理'标签页添加中英文参考文档对")
        print("5. 在'文档翻译'标签页上传要翻译的文档")
        print("6. 系统将基于参考文档中的译法进行翻译")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
