#!/usr/bin/env python3
"""
测试新的参考翻译系统
"""
import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("测试导入模块...")
    
    try:
        from utils.reference_processor import ReferenceProcessor
        print("✅ ReferenceProcessor 导入成功")
    except Exception as e:
        print(f"❌ ReferenceProcessor 导入失败: {e}")
        return False
    
    try:
        from utils.simple_knowledge_base import SimpleKnowledgeBase
        print("✅ SimpleKnowledgeBase 导入成功")
    except Exception as e:
        print(f"❌ SimpleKnowledgeBase 导入失败: {e}")
        return False
    
    try:
        from services.reference_translation_service import ReferenceTranslationService
        print("✅ ReferenceTranslationService 导入成功")
    except Exception as e:
        print(f"❌ ReferenceTranslationService 导入失败: {e}")
        return False
    
    return True

def test_knowledge_base():
    """测试知识库"""
    print("\n测试知识库...")
    
    try:
        from utils.simple_knowledge_base import SimpleKnowledgeBase
        
        # 创建知识库实例
        kb = SimpleKnowledgeBase("test_kb")
        print("✅ 知识库创建成功")
        
        # 获取统计信息
        stats = kb.get_reference_statistics()
        print(f"📊 知识库统计: {stats}")
        
        # 测试搜索
        results = kb.search_similar_content("测试", "zh", 1)
        print(f"🔍 搜索结果: {len(results)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 知识库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_document_processor():
    """测试文档处理器"""
    print("\n测试文档处理器...")
    
    try:
        from utils.reference_processor import ReferenceProcessor
        
        processor = ReferenceProcessor()
        print("✅ 文档处理器创建成功")
        
        # 测试文档验证
        test_file = "2022_ar_e.docx"
        if os.path.exists(test_file):
            is_valid, error_msg = processor.validate_document(test_file)
            print(f"📄 文档验证: {'✅ 有效' if is_valid else f'❌ 无效 - {error_msg}'}")
            
            if is_valid:
                basic_info = processor.extract_basic_info(test_file)
                print(f"📋 基本信息:")
                for key, value in basic_info.items():
                    print(f"  {key}: {value}")
                
                # 测试文档分割
                pages = processor.split_document_by_pages(test_file)
                print(f"📄 文档分割: 共 {len(pages)} 页")
                
                if pages:
                    first_page = pages[0]
                    print(f"第一页预览: {first_page.get('content', '')[:100]}...")
        else:
            print(f"⚠️ 测试文档不存在: {test_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文档处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_docs():
    """创建示例文档"""
    print("\n创建示例文档...")
    
    try:
        from docx import Document
        
        # 创建中文文档
        zh_doc = Document()
        zh_doc.add_heading('公司年度报告', 0)
        zh_doc.add_paragraph('本公司是一家专业从事技术开发的企业。')
        zh_doc.add_paragraph('董事会负责公司的重大决策。')
        zh_doc.add_paragraph('本年度公司业绩表现良好，营业收入增长显著。')
        zh_doc.save('sample_zh.docx')
        print("✅ 创建中文示例文档")
        
        # 创建英文文档
        en_doc = Document()
        en_doc.add_heading('Annual Report', 0)
        en_doc.add_paragraph('The Company is a professional enterprise engaged in technology development.')
        en_doc.add_paragraph('The Board of Directors is responsible for major decisions of the company.')
        en_doc.add_paragraph('The company performed well this year with significant growth in operating revenue.')
        en_doc.save('sample_en.docx')
        print("✅ 创建英文示例文档")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建示例文档失败: {e}")
        return False

def test_add_reference():
    """测试添加参考文档"""
    print("\n测试添加参考文档...")
    
    try:
        from services.reference_translation_service import ReferenceTranslationService
        
        service = ReferenceTranslationService()
        
        # 检查示例文档
        if os.path.exists('sample_zh.docx') and os.path.exists('sample_en.docx'):
            print("📚 使用示例文档进行测试")
            
            result = service.add_reference_documents([('sample_zh.docx', 'sample_en.docx')])
            
            if result.get('success', False):
                print(f"✅ 成功添加参考文档")
                stats = result.get('knowledge_base_stats', {})
                print(f"📊 知识库统计: {stats}")
            else:
                print(f"❌ 添加失败: {result.get('error', '未知错误')}")
        else:
            print("⚠️ 示例文档不存在，跳过测试")
        
        return True
        
    except Exception as e:
        print(f"❌ 添加参考文档测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_translation_preview():
    """测试翻译预览"""
    print("\n测试翻译预览...")
    
    try:
        from services.reference_translation_service import ReferenceTranslationService
        
        service = ReferenceTranslationService()
        
        # 测试预览功能
        test_files = ['2022_ar_e.docx', 'sample_zh.docx', 'sample_en.docx']
        
        for test_file in test_files:
            if os.path.exists(test_file):
                print(f"📄 测试文件: {test_file}")
                
                preview = service.get_translation_preview(test_file)
                
                if preview.get('success', False):
                    file_info = preview.get('file_info', {})
                    print(f"  文件大小: {file_info.get('file_size_mb', 0):.2f} MB")
                    print(f"  总页数: {file_info.get('total_pages', 0)}")
                    print(f"  检测语言: {file_info.get('detected_language', '未知')}")
                else:
                    print(f"  ❌ 预览失败: {preview.get('error', '未知错误')}")
                
                break  # 只测试第一个存在的文件
        
        return True
        
    except Exception as e:
        print(f"❌ 翻译预览测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 新参考翻译系统测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 6
    
    # 1. 测试导入
    if test_imports():
        success_count += 1
    
    # 2. 测试知识库
    if test_knowledge_base():
        success_count += 1
    
    # 3. 测试文档处理器
    if test_document_processor():
        success_count += 1
    
    # 4. 创建示例文档
    if create_sample_docs():
        success_count += 1
    
    # 5. 测试添加参考文档
    if test_add_reference():
        success_count += 1
    
    # 6. 测试翻译预览
    if test_translation_preview():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"测试完成: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("✅ 所有测试通过！")
        print("\n📋 下一步:")
        print("1. 运行 python reference_app.py 启动Gradio界面")
        print("2. 在界面中添加中英文参考文档对")
        print("3. 上传要翻译的文档进行翻译")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
