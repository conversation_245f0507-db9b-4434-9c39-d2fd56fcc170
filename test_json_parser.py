#!/usr/bin/env python3
"""
测试JSON解析器
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.json_parser import RobustJSONParser, parse_review_response, parse_translation_response

def test_control_characters():
    """测试控制字符处理"""
    print("测试控制字符处理...")
    
    # 包含控制字符的JSON
    bad_json = '{\n    "score": 8,\x0c\n    "accuracy_score": 7,\n    "needs_revision": false\n}'
    
    print(f"原始JSON (包含控制字符): {repr(bad_json)}")
    
    # 使用强化解析器
    result = RobustJSONParser.parse(bad_json)
    print(f"解析结果: {result}")
    
    # 测试清理函数
    cleaned = RobustJSONParser.clean_text(bad_json)
    print(f"清理后: {repr(cleaned)}")

def test_malformed_json():
    """测试格式错误的JSON"""
    print("\n测试格式错误的JSON...")
    
    # 各种格式错误的JSON
    test_cases = [
        # 缺少引号
        '{score: 8, accuracy_score: 7, needs_revision: false}',
        
        # 尾随逗号
        '{"score": 8, "accuracy_score": 7, "needs_revision": false,}',
        
        # 不完整的JSON
        '{"score": 8, "accuracy_score": 7',
        
        # 包含额外文本
        'Here is the result: {"score": 8, "accuracy_score": 7} and some more text',
        
        # 多个JSON对象
        '{"score": 8} {"accuracy_score": 7}',
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_case}")
        result = RobustJSONParser.parse(test_case)
        print(f"解析结果: {result}")

def test_review_response():
    """测试校对响应解析"""
    print("\n测试校对响应解析...")
    
    # 模拟AI返回的校对响应
    test_responses = [
        # 正常JSON
        '''
        {
            "score": 8,
            "accuracy_score": 8,
            "consistency_score": 7,
            "terminology_score": 8,
            "issues": ["术语不一致"],
            "suggestions": ["使用标准术语"],
            "reference_usage": "良好",
            "needs_revision": false,
            "feedback": "整体质量良好"
        }
        ''',
        
        # 包含控制字符的JSON
        '{\n    "score": 6,\x0c\n    "accuracy_score": 6,\n    "needs_revision": true\n}',
        
        # 非JSON格式
        '''
        评分: 7
        准确性评分: 7
        一致性评分: 6
        问题: 术语翻译不一致
        建议: 参考历史翻译
        需要修订: 是
        ''',
        
        # 混合格式
        '''
        校对结果如下：
        {
            "score": 8,
            "needs_revision": false
        }
        以上是详细评估。
        ''',
    ]
    
    for i, response in enumerate(test_responses, 1):
        print(f"\n校对响应 {i}:")
        print(f"原始响应: {repr(response[:100])}...")
        
        result = parse_review_response(response)
        print(f"解析结果: {result}")

def test_translation_response():
    """测试翻译响应解析"""
    print("\n测试翻译响应解析...")
    
    # 模拟AI返回的翻译响应
    test_responses = [
        # 正常JSON
        '''
        {
            "translated_text": "This is the translated content.",
            "notes": "Translation completed successfully."
        }
        ''',
        
        # 包含控制字符
        '{\n    "translated_text": "翻译内容",\x0c\n    "notes": "翻译说明"\n}',
        
        # 非JSON格式
        '''
        翻译结果:
        这是翻译后的内容。
        
        说明:
        翻译已完成。
        ''',
        
        # 纯文本
        '这是直接的翻译内容，没有JSON格式。',
    ]
    
    for i, response in enumerate(test_responses, 1):
        print(f"\n翻译响应 {i}:")
        print(f"原始响应: {repr(response[:100])}...")
        
        result = parse_translation_response(response)
        print(f"解析结果: {result}")

def test_edge_cases():
    """测试边界情况"""
    print("\n测试边界情况...")
    
    edge_cases = [
        "",  # 空字符串
        "   ",  # 只有空格
        "null",  # null值
        "true",  # 布尔值
        "123",  # 数字
        "{",  # 不完整的括号
        "}",  # 单独的右括号
        '{"key": "value with \\"quotes\\""}',  # 转义引号
        '{"key": "value with \\n newline"}',  # 转义换行符
    ]
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n边界情况 {i}: {repr(case)}")
        result = RobustJSONParser.parse(case)
        print(f"解析结果: {result}")

def main():
    """主测试函数"""
    print("🧪 JSON解析器测试")
    print("=" * 60)
    
    try:
        # 1. 测试控制字符处理
        test_control_characters()
        
        # 2. 测试格式错误的JSON
        test_malformed_json()
        
        # 3. 测试校对响应解析
        test_review_response()
        
        # 4. 测试翻译响应解析
        test_translation_response()
        
        # 5. 测试边界情况
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("✅ JSON解析器测试完成")
        print("=" * 60)
        
        print("\n📋 修复说明:")
        print("1. 添加了强化的JSON解析器，能处理控制字符")
        print("2. 改进了错误处理和回退机制")
        print("3. 更新了智能体使用新的解析函数")
        print("4. 优化了AI模型的提示词格式")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
