# 基于参考文档的翻译系统

## 系统概述

本系统已重构为基于用户提供的参考文档进行翻译的模式。用户需要提供成对的中英文参考文档，系统会学习其中的翻译模式和术语对照，然后应用到新文档的翻译中。

## 主要特性

### 🔄 重构亮点
- **用户驱动**: 不再依赖自动提取公司信息，改为用户直接提供参考文档对
- **离线优先**: 使用简化的本地匹配算法，不依赖外部AI模型
- **参考学习**: 从用户提供的中英文文档对中学习翻译模式
- **术语一致性**: 自动提取并应用参考文档中的术语翻译

### 📚 核心功能
1. **参考文档管理**: 添加、管理中英文参考文档对
2. **智能匹配**: 基于内容相似度匹配参考翻译
3. **术语对照**: 自动提取和应用术语翻译
4. **格式保持**: 保持原文档的格式和结构
5. **质量控制**: 多轮校对和质量评估

## 系统架构

```
reference_app.py                 # Gradio用户界面
├── services/
│   └── reference_translation_service.py  # 翻译服务
├── utils/
│   ├── reference_processor.py            # 参考文档处理
│   ├── simple_knowledge_base.py          # 简化知识库
│   ├── cost_estimator.py                 # 成本估算
│   └── translation_history.py            # 翻译历史
├── agents/
│   ├── translation_agent.py              # 翻译智能体
│   └── review_agent.py                   # 校对智能体
└── config.py                             # 配置文件
```

## 使用流程

### 1. 准备参考文档
- 准备成对的中英文DOCX文档
- 确保文档内容对应（如同一公司的中英文年报）
- 文档应包含丰富的专业术语和表达方式

### 2. 启动系统
```bash
python reference_app.py
```

### 3. 添加参考文档
- 在"参考文档管理"标签页
- 上传中文参考文档（可多个）
- 上传对应的英文参考文档（数量需相等）
- 点击"添加参考文档对"

### 4. 翻译文档
- 在"文档翻译"标签页
- 上传要翻译的文档
- 选择翻译方向（中译英/英译中）
- 点击"预览文档信息"查看成本估算
- 点击"开始翻译"执行翻译

## 核心模块说明

### ReferenceProcessor (参考文档处理器)
- **功能**: 处理用户上传的参考文档
- **主要方法**:
  - `extract_basic_info()`: 提取文档基本信息
  - `split_document_by_pages()`: 按页分割文档
  - `extract_bilingual_pairs()`: 提取双语对照内容
  - `extract_terminology_pairs()`: 提取术语对照

### SimpleKnowledgeBase (简化知识库)
- **功能**: 存储和检索参考翻译内容
- **存储方式**: 本地JSON文件
- **主要方法**:
  - `add_reference_pair()`: 添加参考文档对
  - `search_similar_content()`: 搜索相似内容
  - `search_terminology()`: 搜索术语翻译

### ReferenceTranslationService (翻译服务)
- **功能**: 协调整个翻译流程
- **主要方法**:
  - `add_reference_documents()`: 添加参考文档
  - `translate_document()`: 翻译文档
  - `get_translation_preview()`: 获取翻译预览

## 配置说明

### config.py 主要配置项
```python
# API配置
OPENROUTER_API_KEY = "your_api_key"
OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"

# 模型配置
TRANSLATION_MODEL = "anthropic/claude-3.5-sonnet"
REVIEW_MODEL = "anthropic/claude-3.5-sonnet"

# 存储路径
TRANSLATION_HISTORY_DIR = "translation_history"
CHROMA_PERSIST_DIRECTORY = "chroma_db"

# 翻译参数
MAX_RETRY_ATTEMPTS = 3
CHUNK_SIZE = 1000
```

## 文件结构

### 输入文件
- 支持格式: `.docx`
- 文件大小: 最大50MB
- 语言: 中文、英文

### 输出文件
- 格式: `.docx`
- 命名规则: `原文件名_翻译方向_时间戳.docx`
- 位置: `translation_history/` 目录

### 知识库文件
```
knowledge_base/
├── documents.json      # 文档内容
├── metadata.json       # 元数据
├── terminology.json    # 术语对照
└── references.json     # 参考文档信息
```

## 测试和验证

### 运行测试
```bash
python test_new_system.py
```

### 测试内容
1. 模块导入测试
2. 知识库功能测试
3. 文档处理器测试
4. 示例文档创建
5. 参考文档添加测试
6. 翻译预览测试

## 优势对比

### 相比原系统
| 特性 | 原系统 | 新系统 |
|------|--------|--------|
| 数据来源 | 自动爬取港交所 | 用户提供参考文档 |
| 依赖性 | 依赖外部API和模型 | 本地化处理 |
| 准确性 | 依赖自动提取准确性 | 基于用户确认的参考内容 |
| 灵活性 | 限定港交所文档 | 支持任意领域文档 |
| 可控性 | 系统自动决策 | 用户完全控制参考内容 |

## 注意事项

### 参考文档质量
- 确保中英文文档内容对应
- 参考文档应包含丰富的专业术语
- 建议使用同一机构的官方翻译文档

### 系统限制
- 目前仅支持DOCX格式
- 翻译质量依赖参考文档的质量和相关性
- 复杂表格和图片处理能力有限

### 性能优化
- 参考文档数量建议控制在10对以内
- 单个文档大小建议不超过50MB
- 定期清理不需要的参考文档

## 故障排除

### 常见问题
1. **导入失败**: 检查依赖包是否安装完整
2. **文档验证失败**: 确认文档格式为DOCX且未损坏
3. **翻译质量差**: 增加相关领域的参考文档
4. **成本超限**: 调整预算设置或减少文档长度

### 日志查看
- 应用日志: `reference_translation_app.log`
- 错误信息会显示在Gradio界面中

## 未来改进

### 计划功能
1. 支持更多文档格式（PDF、TXT等）
2. 改进术语提取算法
3. 增加翻译记忆功能
4. 支持批量翻译
5. 添加翻译质量评估指标

### 技术优化
1. 优化相似度匹配算法
2. 改进文档分割逻辑
3. 增加并行处理能力
4. 优化存储结构

---

## 快速开始

1. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **配置API密钥**:
   编辑 `config.py` 文件，设置 OpenRouter API 密钥

3. **启动应用**:
   ```bash
   python reference_app.py
   ```

4. **访问界面**:
   打开浏览器访问 `http://localhost:7860`

5. **开始使用**:
   - 上传参考文档对
   - 翻译新文档

---

**注意**: 本系统重构为基于参考文档的翻译模式，提供了更高的可控性和准确性。用户需要自行提供高质量的参考文档对以获得最佳翻译效果。
