"""
文档处理模块 - 处理docx文件的读取、分页、格式保留
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
import logging

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """文档处理器"""

    def __init__(self):
        self.supported_formats = [".docx"]

    def extract_company_info(self, doc_path: str) -> Dict[str, str]:
        """
        从文档中提取公司信息

        Args:
            doc_path: 文档路径

        Returns:
            包含公司信息的字典
        """
        try:
            doc = Document(doc_path)
            company_info = {
                "company_name_zh": "",
                "company_name_en": "",
                "stock_code": "",
                "report_type": "",
                "report_year": "",
            }

            # 从前几页提取公司信息
            text_content = ""
            for i, paragraph in enumerate(doc.paragraphs[:50]):  # 只检查前50段
                text_content += paragraph.text + "\n"

            # 提取股票代码 (格式: 数字.HK 或 数字)
            stock_code_pattern = r"(\d{4,5})(?:\.HK)?"
            stock_matches = re.findall(stock_code_pattern, text_content)
            if stock_matches:
                company_info["stock_code"] = stock_matches[0]

            # 提取报告类型
            report_types = {
                "年报": "annual_report",
                "annual report": "annual_report",
                "中期报告": "interim_report",
                "interim report": "interim_report",
                "季度报告": "quarterly_report",
                "quarterly report": "quarterly_report",
            }

            for keyword, report_type in report_types.items():
                if keyword.lower() in text_content.lower():
                    company_info["report_type"] = report_type
                    break

            # 提取年份
            year_pattern = r"20\d{2}"
            year_matches = re.findall(year_pattern, text_content)
            if year_matches:
                company_info["report_year"] = year_matches[-1]  # 取最后一个年份

            # 提取公司名称（中英文）
            lines = text_content.split("\n")[:20]  # 前20行
            for line in lines:
                line = line.strip()
                if len(line) > 5 and (
                    "有限公司" in line or "Limited" in line or "Ltd" in line
                ):
                    if any("\u4e00" <= char <= "\u9fff" for char in line):  # 包含中文
                        if not company_info["company_name_zh"]:
                            company_info["company_name_zh"] = line
                    else:  # 英文
                        if not company_info["company_name_en"]:
                            company_info["company_name_en"] = line

            logger.info(f"提取的公司信息: {company_info}")
            return company_info

        except Exception as e:
            logger.error(f"提取公司信息失败: {e}")
            return {}

    def split_document_by_pages(
        self, doc_path: str, pages_per_chunk: int = 1
    ) -> List[Dict]:
        """
        按页分割文档

        Args:
            doc_path: 文档路径
            pages_per_chunk: 每个块的页数

        Returns:
            分页后的文档块列表
        """
        try:
            doc = Document(doc_path)
            pages = []
            current_page = {
                "page_number": 1,
                "content": "",
                "paragraphs": [],
                "tables": [],
                "images": [],
            }

            paragraph_count = 0

            for paragraph in doc.paragraphs:
                # 检查是否为分页符
                if self._is_page_break(paragraph):
                    if current_page["content"].strip():
                        pages.append(current_page)
                    current_page = {
                        "page_number": len(pages) + 1,
                        "content": "",
                        "paragraphs": [],
                        "tables": [],
                        "images": [],
                    }
                    paragraph_count = 0
                    continue

                # 添加段落内容
                if paragraph.text.strip():
                    current_page["content"] += paragraph.text + "\n"
                    current_page["paragraphs"].append(
                        {
                            "text": paragraph.text,
                            "style": (
                                paragraph.style.name if paragraph.style else "Normal"
                            ),
                            "alignment": paragraph.alignment,
                        }
                    )
                    paragraph_count += 1

                # 简单的分页逻辑：每30个段落为一页
                if paragraph_count >= 30:
                    pages.append(current_page)
                    current_page = {
                        "page_number": len(pages) + 1,
                        "content": "",
                        "paragraphs": [],
                        "tables": [],
                        "images": [],
                    }
                    paragraph_count = 0

            # 添加最后一页
            if current_page["content"].strip():
                pages.append(current_page)

            # 处理表格
            for table in doc.tables:
                # 简单地将表格分配给第一页（实际应用中需要更复杂的逻辑）
                if pages:
                    table_data = []
                    for row in table.rows:
                        row_data = [cell.text for cell in row.cells]
                        table_data.append(row_data)
                    pages[0]["tables"].append(table_data)

            logger.info(f"文档分割完成，共 {len(pages)} 页")
            return pages

        except Exception as e:
            logger.error(f"文档分割失败: {e}")
            return []

    def _is_page_break(self, paragraph) -> bool:
        """检查段落是否包含分页符"""
        try:
            for run in paragraph.runs:
                if "\f" in run.text or "\x0c" in run.text:
                    return True
            return False
        except:
            return False

    def create_translated_document(
        self, original_doc_path: str, translated_pages: List[Dict], output_path: str
    ) -> str:
        """
        创建翻译后的文档，保持原有格式

        Args:
            original_doc_path: 原始文档路径
            translated_pages: 翻译后的页面列表
            output_path: 输出路径

        Returns:
            输出文档路径
        """
        try:
            # 创建新文档
            new_doc = Document()

            # 复制原文档的样式设置
            original_doc = Document(original_doc_path)

            for page in translated_pages:
                # 添加页面内容
                for para_info in page.get("paragraphs", []):
                    p = new_doc.add_paragraph()
                    p.text = para_info.get("translated_text", para_info.get("text", ""))

                    # 尝试保持原有样式
                    try:
                        if para_info.get("style"):
                            p.style = para_info["style"]
                        if para_info.get("alignment"):
                            p.alignment = para_info["alignment"]
                    except:
                        pass  # 如果样式设置失败，使用默认样式

                # 添加表格
                for table_data in page.get("tables", []):
                    if table_data:
                        table = new_doc.add_table(
                            rows=len(table_data), cols=len(table_data[0])
                        )
                        for i, row_data in enumerate(table_data):
                            for j, cell_text in enumerate(row_data):
                                table.cell(i, j).text = cell_text

                # 添加分页符（除了最后一页）
                if page != translated_pages[-1]:
                    new_doc.add_page_break()

            # 保存文档
            new_doc.save(output_path)
            logger.info(f"翻译文档已保存: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"创建翻译文档失败: {e}")
            raise

    def validate_document(self, doc_path: str) -> Tuple[bool, str]:
        """
        验证文档是否有效

        Args:
            doc_path: 文档路径

        Returns:
            (是否有效, 错误信息)
        """
        try:
            if not os.path.exists(doc_path):
                return False, "文件不存在"

            file_size = os.path.getsize(doc_path) / (1024 * 1024)  # MB
            if file_size > 50:  # 50MB限制
                return False, f"文件过大: {file_size:.1f}MB，最大支持50MB"

            # 尝试打开文档
            doc = Document(doc_path)

            # 检查是否有内容
            if len(doc.paragraphs) == 0:
                return False, "文档为空"

            return True, ""

        except Exception as e:
            return False, f"文档格式错误: {str(e)}"
