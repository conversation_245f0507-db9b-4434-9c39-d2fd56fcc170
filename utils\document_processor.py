"""
文档处理模块 - 处理docx文件的读取、分页、格式保留
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
import logging

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)


class DocumentProcessor:
    """文档处理器"""

    def __init__(self):
        self.supported_formats = [".docx"]

    def extract_company_info(self, doc_path: str) -> Dict[str, str]:
        """
        从文档中提取公司信息

        Args:
            doc_path: 文档路径

        Returns:
            包含公司信息的字典
        """
        try:
            doc = Document(doc_path)
            company_info = {
                "company_name_zh": "",
                "company_name_en": "",
                "stock_code": "",
                "report_type": "",
                "report_year": "",
            }

            # 从前几页提取公司信息，包括表格内容
            text_content = ""
            for paragraph in doc.paragraphs[:100]:  # 检查前100段
                text_content += paragraph.text + "\n"

            # 同时检查表格中的内容
            for table in doc.tables[:5]:  # 检查前5个表格
                for row in table.rows:
                    for cell in row.cells:
                        text_content += cell.text + "\n"

            # 改进的股票代码提取 - 更精确的模式
            stock_code_patterns = [
                r"股票代码[：:\s]*(\d{4,5})",  # 股票代码：0700
                r"Stock Code[：:\s]*(\d{4,5})",  # Stock Code: 0700
                r"代码[：:\s]*(\d{4,5})",  # 代码：0700
                r"Code[：:\s]*(\d{4,5})",  # Code: 0700
                r"(\d{4})\.HK",  # 0700.HK
                r"HK(\d{4})",  # HK0700
                r"港股代码[：:\s]*(\d{4,5})",  # 港股代码：0700
                r"证券代码[：:\s]*(\d{4,5})",  # 证券代码：0700
                r"股份代号[：:\s]*(\d{4,5})",  # 股份代号：0700
            ]

            for pattern in stock_code_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    # 过滤掉年份（通常是20xx）和其他不合理的数字
                    valid_codes = [
                        code
                        for code in matches
                        if not code.startswith("20")
                        and len(code) >= 3
                        and len(code) <= 5
                    ]
                    if valid_codes:
                        company_info["stock_code"] = valid_codes[0].zfill(
                            4
                        )  # 补齐到4位
                        break

            # 改进的报告类型提取
            report_type_patterns = {
                "annual_report": [
                    r"年度报告",
                    r"年报",
                    r"Annual Report",
                    r"年度業績",
                    r"全年業績",
                    r"年度财务报告",
                    r"年度財務報告",
                    r"年度业绩",
                ],
                "interim_report": [
                    r"中期报告",
                    r"中期報告",
                    r"Interim Report",
                    r"半年报",
                    r"半年報",
                    r"中期業績",
                    r"中期财务报告",
                    r"中期业绩",
                ],
                "quarterly_report": [
                    r"季度报告",
                    r"季報",
                    r"Quarterly Report",
                    r"季度業績",
                    r"第.*季度",
                    r"Q[1-4].*报告",
                    r"季度业绩",
                ],
            }

            for report_type, patterns in report_type_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, text_content, re.IGNORECASE):
                        company_info["report_type"] = report_type
                        break
                if company_info["report_type"]:
                    break

            # 改进的年份提取 - 避免误识别股票代码
            year_patterns = [
                r"(\d{4})年度",  # 2022年度
                r"(\d{4})年",  # 2022年
                r"Year (\d{4})",  # Year 2022
                r"截至(\d{4})年",  # 截至2022年
                r"For the year ended.*?(\d{4})",  # For the year ended 31 December 2022
                r"年度.*?(\d{4})",  # 年度2022
            ]

            for pattern in year_patterns:
                matches = re.findall(pattern, text_content)
                if matches:
                    # 只取合理的年份范围
                    valid_years = [
                        year for year in matches if 2000 <= int(year) <= 2030
                    ]
                    if valid_years:
                        company_info["report_year"] = valid_years[-1]  # 取最后一个年份
                        break

            # 改进的公司名称提取
            company_name_patterns = [
                # 中文公司名称模式
                r"([^，。\n]{2,50}(?:有限公司|股份有限公司|集团有限公司|控股有限公司|集團有限公司|控股有限責任公司))",
                r"([^，。\n]{2,50}(?:有限責任公司|股份有限責任公司))",
                r"([^，。\n]{2,30}(?:集团|集團|控股|科技|技术|技術|投资|投資|发展|發展|实业|實業|国际|國際))",  # 添加更多中文公司后缀
                # 英文公司名称模式
                r"([A-Z][A-Za-z\s&]{5,60}(?:Limited|Ltd\.?|Corporation|Corp\.?|Company|Co\.?))",
                r"([A-Z][A-Za-z\s&]{5,60}(?:Holdings|Group|International)(?:\s+Limited|\s+Ltd\.?)?)",
                r"([A-Z][A-Za-z\s&]{3,40}(?:Group|Holdings|Corp|Inc|Technology|Tech))",  # 添加更多英文公司后缀
            ]

            for pattern in company_name_patterns:
                matches = re.findall(pattern, text_content)
                for match in matches:
                    match = match.strip()
                    if len(match) > 5 and len(match) < 100:  # 过滤太短或太长的匹配
                        # 判断是中文还是英文
                        if any("\u4e00" <= char <= "\u9fff" for char in match):
                            if not company_info["company_name_zh"]:
                                company_info["company_name_zh"] = match
                        else:
                            if not company_info["company_name_en"]:
                                company_info["company_name_en"] = match

            # 特殊处理：从文档标题或页眉页脚提取
            if (
                not company_info["company_name_zh"]
                and not company_info["company_name_en"]
            ):
                # 检查文档的核心属性
                try:
                    core_props = doc.core_properties
                    if core_props.title:
                        title = core_props.title.strip()
                        if len(title) > 5:
                            if any("\u4e00" <= char <= "\u9fff" for char in title):
                                company_info["company_name_zh"] = title
                            else:
                                company_info["company_name_en"] = title
                except:
                    pass

                # 检查前几个段落的文本（通常是标题）
                for paragraph in doc.paragraphs[:15]:
                    if paragraph.text.strip():
                        text = paragraph.text.strip()
                        # 检查是否包含公司关键词且长度合理
                        if (
                            any(
                                keyword in text
                                for keyword in [
                                    "有限公司",
                                    "Limited",
                                    "Holdings",
                                    "Group",
                                ]
                            )
                            and len(text) > 5
                            and len(text) < 100
                        ):
                            if any("\u4e00" <= char <= "\u9fff" for char in text):
                                if not company_info["company_name_zh"]:
                                    company_info["company_name_zh"] = text
                            else:
                                if not company_info["company_name_en"]:
                                    company_info["company_name_en"] = text

            # 清理提取的信息
            for key in company_info:
                if isinstance(company_info[key], str):
                    company_info[key] = company_info[key].strip()

            logger.info(f"提取的公司信息: {company_info}")
            return company_info

        except Exception as e:
            logger.error(f"提取公司信息失败: {e}")
            return {}

    def split_document_by_pages(
        self, doc_path: str, pages_per_chunk: int = 1
    ) -> List[Dict]:
        """
        按页分割文档

        Args:
            doc_path: 文档路径
            pages_per_chunk: 每个块的页数

        Returns:
            分页后的文档块列表
        """
        try:
            doc = Document(doc_path)
            pages = []
            current_page = {
                "page_number": 1,
                "content": "",
                "paragraphs": [],
                "tables": [],
                "images": [],
            }

            paragraph_count = 0

            for paragraph in doc.paragraphs:
                # 检查是否为分页符
                if self._is_page_break(paragraph):
                    if current_page["content"].strip():
                        pages.append(current_page)
                    current_page = {
                        "page_number": len(pages) + 1,
                        "content": "",
                        "paragraphs": [],
                        "tables": [],
                        "images": [],
                    }
                    paragraph_count = 0
                    continue

                # 添加段落内容
                if paragraph.text.strip():
                    current_page["content"] += paragraph.text + "\n"
                    current_page["paragraphs"].append(
                        {
                            "text": paragraph.text,
                            "style": (
                                paragraph.style.name if paragraph.style else "Normal"
                            ),
                            "alignment": paragraph.alignment,
                        }
                    )
                    paragraph_count += 1

                # 简单的分页逻辑：每30个段落为一页
                if paragraph_count >= 30:
                    pages.append(current_page)
                    current_page = {
                        "page_number": len(pages) + 1,
                        "content": "",
                        "paragraphs": [],
                        "tables": [],
                        "images": [],
                    }
                    paragraph_count = 0

            # 添加最后一页
            if current_page["content"].strip():
                pages.append(current_page)

            # 处理表格
            for table in doc.tables:
                # 简单地将表格分配给第一页（实际应用中需要更复杂的逻辑）
                if pages:
                    table_data = []
                    for row in table.rows:
                        row_data = [cell.text for cell in row.cells]
                        table_data.append(row_data)
                    pages[0]["tables"].append(table_data)

            logger.info(f"文档分割完成，共 {len(pages)} 页")
            return pages

        except Exception as e:
            logger.error(f"文档分割失败: {e}")
            return []

    def _is_page_break(self, paragraph) -> bool:
        """检查段落是否包含分页符"""
        try:
            for run in paragraph.runs:
                if "\f" in run.text or "\x0c" in run.text:
                    return True
            return False
        except:
            return False

    def create_translated_document(
        self, original_doc_path: str, translated_pages: List[Dict], output_path: str
    ) -> str:
        """
        创建翻译后的文档，保持原有格式

        Args:
            original_doc_path: 原始文档路径
            translated_pages: 翻译后的页面列表
            output_path: 输出路径

        Returns:
            输出文档路径
        """
        try:
            # 创建新文档
            new_doc = Document()

            # 复制原文档的样式设置
            original_doc = Document(original_doc_path)

            for page in translated_pages:
                # 添加页面内容
                for para_info in page.get("paragraphs", []):
                    p = new_doc.add_paragraph()
                    p.text = para_info.get("translated_text", para_info.get("text", ""))

                    # 尝试保持原有样式
                    try:
                        if para_info.get("style"):
                            p.style = para_info["style"]
                        if para_info.get("alignment"):
                            p.alignment = para_info["alignment"]
                    except:
                        pass  # 如果样式设置失败，使用默认样式

                # 添加表格
                for table_data in page.get("tables", []):
                    if table_data:
                        table = new_doc.add_table(
                            rows=len(table_data), cols=len(table_data[0])
                        )
                        for i, row_data in enumerate(table_data):
                            for j, cell_text in enumerate(row_data):
                                table.cell(i, j).text = cell_text

                # 添加分页符（除了最后一页）
                if page != translated_pages[-1]:
                    new_doc.add_page_break()

            # 保存文档
            new_doc.save(output_path)
            logger.info(f"翻译文档已保存: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"创建翻译文档失败: {e}")
            raise

    def validate_document(self, doc_path: str) -> Tuple[bool, str]:
        """
        验证文档是否有效

        Args:
            doc_path: 文档路径

        Returns:
            (是否有效, 错误信息)
        """
        try:
            if not os.path.exists(doc_path):
                return False, "文件不存在"

            file_size = os.path.getsize(doc_path) / (1024 * 1024)  # MB
            if file_size > 50:  # 50MB限制
                return False, f"文件过大: {file_size:.1f}MB，最大支持50MB"

            # 尝试打开文档
            doc = Document(doc_path)

            # 检查是否有内容
            if len(doc.paragraphs) == 0:
                return False, "文档为空"

            return True, ""

        except Exception as e:
            return False, f"文档格式错误: {str(e)}"
