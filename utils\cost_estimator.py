"""
成本估算模块 - 估算翻译成本和管理预算
"""
import os
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import tiktoken
import logging
from config import Config, MODEL_PRICING

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)

class CostEstimator:
    """成本估算器"""
    
    def __init__(self):
        self.encoding = tiktoken.get_encoding("cl100k_base")
        self.cost_history_file = os.path.join(Config.TRANSLATION_HISTORY_DIR, "cost_history.json")
        self.daily_usage_file = os.path.join(Config.TRANSLATION_HISTORY_DIR, "daily_usage.json")
        
    def estimate_document_cost(self, pages: List[Dict], translation_direction: str,
                             model_name: str = None) -> Dict:
        """
        估算文档翻译成本
        
        Args:
            pages: 文档页面列表
            translation_direction: 翻译方向
            model_name: 使用的模型名称
            
        Returns:
            成本估算结果
        """
        try:
            if not model_name:
                model_name = Config.DEFAULT_TRANSLATION_MODEL
            
            # 获取模型价格
            pricing = MODEL_PRICING.get(model_name, {"input": 0.01, "output": 0.03})
            
            total_input_tokens = 0
            total_output_tokens = 0
            page_estimates = []
            
            for page in pages:
                # 估算输入tokens
                content = page.get('content', '')
                input_tokens = len(self.encoding.encode(content))
                
                # 添加系统提示和参考翻译的tokens
                system_prompt_tokens = 500  # 估算
                reference_tokens = 2000  # 估算参考翻译的tokens
                total_page_input = input_tokens + system_prompt_tokens + reference_tokens
                
                # 估算输出tokens（通常是输入的0.8-1.2倍）
                output_tokens = int(input_tokens * 1.0)
                
                # 考虑校对和可能的修订
                review_input_tokens = total_page_input + output_tokens
                review_output_tokens = 300  # 校对输出较少
                
                # 可能的修订成本（假设30%的页面需要修订）
                revision_probability = 0.3
                revision_input_tokens = int((total_page_input + output_tokens) * revision_probability)
                revision_output_tokens = int(output_tokens * revision_probability)
                
                page_total_input = total_page_input + review_input_tokens + revision_input_tokens
                page_total_output = output_tokens + review_output_tokens + revision_output_tokens
                
                page_cost = (page_total_input / 1000 * pricing["input"] + 
                           page_total_output / 1000 * pricing["output"])
                
                page_estimates.append({
                    'page_number': page.get('page_number', len(page_estimates) + 1),
                    'input_tokens': page_total_input,
                    'output_tokens': page_total_output,
                    'estimated_cost': page_cost,
                    'content_length': len(content)
                })
                
                total_input_tokens += page_total_input
                total_output_tokens += page_total_output
            
            # 计算总成本
            total_cost = (total_input_tokens / 1000 * pricing["input"] + 
                         total_output_tokens / 1000 * pricing["output"])
            
            # 添加10%的缓冲
            total_cost_with_buffer = total_cost * 1.1
            
            estimate_result = {
                'model_name': model_name,
                'total_pages': len(pages),
                'total_input_tokens': total_input_tokens,
                'total_output_tokens': total_output_tokens,
                'estimated_cost': total_cost,
                'estimated_cost_with_buffer': total_cost_with_buffer,
                'cost_per_page': total_cost / len(pages) if pages else 0,
                'page_estimates': page_estimates,
                'pricing_info': pricing,
                'translation_direction': translation_direction,
                'estimated_time_minutes': len(pages) * 2,  # 估算每页2分钟
                'timestamp': datetime.now().isoformat()
            }
            
            # 检查预算限制
            if total_cost_with_buffer > Config.MAX_COST_PER_DOCUMENT:
                estimate_result['budget_warning'] = True
                estimate_result['budget_message'] = f"预估成本 ${total_cost_with_buffer:.2f} 超过预算限制 ${Config.MAX_COST_PER_DOCUMENT:.2f}"
            elif total_cost_with_buffer > Config.COST_WARNING_THRESHOLD:
                estimate_result['budget_warning'] = True
                estimate_result['budget_message'] = f"预估成本 ${total_cost_with_buffer:.2f} 接近预算警告线 ${Config.COST_WARNING_THRESHOLD:.2f}"
            else:
                estimate_result['budget_warning'] = False
            
            logger.info(f"文档成本估算完成：{len(pages)} 页，预估成本 ${total_cost_with_buffer:.2f}")
            return estimate_result
            
        except Exception as e:
            logger.error(f"成本估算失败: {e}")
            return {
                'error': str(e),
                'estimated_cost': 0,
                'budget_warning': True
            }
    
    def track_actual_cost(self, translation_results: List[Dict], company_info: Dict) -> Dict:
        """
        跟踪实际成本
        
        Args:
            translation_results: 翻译结果列表
            company_info: 公司信息
            
        Returns:
            成本跟踪结果
        """
        try:
            total_cost = 0
            total_time = 0
            successful_pages = 0
            
            for result in translation_results:
                if result.get('success', False):
                    total_cost += result.get('cost', 0)
                    total_time += result.get('processing_time', 0)
                    successful_pages += 1
            
            cost_record = {
                'timestamp': datetime.now().isoformat(),
                'company_info': company_info,
                'total_pages': len(translation_results),
                'successful_pages': successful_pages,
                'total_cost': total_cost,
                'total_time_seconds': total_time,
                'average_cost_per_page': total_cost / successful_pages if successful_pages > 0 else 0,
                'average_time_per_page': total_time / successful_pages if successful_pages > 0 else 0,
                'models_used': list(set(r.get('model_used', '') for r in translation_results if r.get('model_used')))
            }
            
            # 保存成本记录
            self._save_cost_record(cost_record)
            
            # 更新每日使用量
            self._update_daily_usage(total_cost)
            
            logger.info(f"实际成本跟踪完成：${total_cost:.4f}，{successful_pages} 页成功")
            return cost_record
            
        except Exception as e:
            logger.error(f"成本跟踪失败: {e}")
            return {'error': str(e)}
    
    def _save_cost_record(self, cost_record: Dict):
        """保存成本记录"""
        try:
            # 读取现有记录
            if os.path.exists(self.cost_history_file):
                with open(self.cost_history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            else:
                history = []
            
            # 添加新记录
            history.append(cost_record)
            
            # 保持最近100条记录
            if len(history) > 100:
                history = history[-100:]
            
            # 保存
            os.makedirs(os.path.dirname(self.cost_history_file), exist_ok=True)
            with open(self.cost_history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.warning(f"保存成本记录失败: {e}")
    
    def _update_daily_usage(self, cost: float):
        """更新每日使用量"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            
            # 读取现有使用量
            if os.path.exists(self.daily_usage_file):
                with open(self.daily_usage_file, 'r', encoding='utf-8') as f:
                    daily_usage = json.load(f)
            else:
                daily_usage = {}
            
            # 更新今日使用量
            if today not in daily_usage:
                daily_usage[today] = {'cost': 0, 'documents': 0}
            
            daily_usage[today]['cost'] += cost
            daily_usage[today]['documents'] += 1
            
            # 清理30天前的记录
            cutoff_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            daily_usage = {date: usage for date, usage in daily_usage.items() if date >= cutoff_date}
            
            # 保存
            os.makedirs(os.path.dirname(self.daily_usage_file), exist_ok=True)
            with open(self.daily_usage_file, 'w', encoding='utf-8') as f:
                json.dump(daily_usage, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.warning(f"更新每日使用量失败: {e}")
    
    def get_cost_statistics(self, days: int = 30) -> Dict:
        """
        获取成本统计信息
        
        Args:
            days: 统计天数
            
        Returns:
            成本统计结果
        """
        try:
            # 读取每日使用量
            if not os.path.exists(self.daily_usage_file):
                return {
                    'total_cost': 0,
                    'total_documents': 0,
                    'daily_average': 0,
                    'days_with_usage': 0
                }
            
            with open(self.daily_usage_file, 'r', encoding='utf-8') as f:
                daily_usage = json.load(f)
            
            # 计算指定天数内的统计
            cutoff_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            recent_usage = {date: usage for date, usage in daily_usage.items() if date >= cutoff_date}
            
            total_cost = sum(usage['cost'] for usage in recent_usage.values())
            total_documents = sum(usage['documents'] for usage in recent_usage.values())
            days_with_usage = len(recent_usage)
            
            statistics = {
                'period_days': days,
                'total_cost': total_cost,
                'total_documents': total_documents,
                'daily_average': total_cost / days if days > 0 else 0,
                'days_with_usage': days_with_usage,
                'average_cost_per_document': total_cost / total_documents if total_documents > 0 else 0,
                'daily_breakdown': recent_usage
            }
            
            return statistics
            
        except Exception as e:
            logger.error(f"获取成本统计失败: {e}")
            return {'error': str(e)}
    
    def check_budget_status(self, estimated_cost: float) -> Dict:
        """
        检查预算状态
        
        Args:
            estimated_cost: 预估成本
            
        Returns:
            预算状态
        """
        try:
            # 获取今日已使用成本
            today_stats = self.get_cost_statistics(days=1)
            today_used = today_stats.get('total_cost', 0)
            
            # 获取本月已使用成本
            month_stats = self.get_cost_statistics(days=30)
            month_used = month_stats.get('total_cost', 0)
            
            # 检查各种预算限制
            budget_status = {
                'estimated_cost': estimated_cost,
                'today_used': today_used,
                'month_used': month_used,
                'can_proceed': True,
                'warnings': [],
                'recommendations': []
            }
            
            # 检查单文档预算限制
            if estimated_cost > Config.MAX_COST_PER_DOCUMENT:
                budget_status['can_proceed'] = False
                budget_status['warnings'].append(f"预估成本 ${estimated_cost:.2f} 超过单文档限制 ${Config.MAX_COST_PER_DOCUMENT:.2f}")
            
            # 检查警告阈值
            elif estimated_cost > Config.COST_WARNING_THRESHOLD:
                budget_status['warnings'].append(f"预估成本 ${estimated_cost:.2f} 超过警告阈值 ${Config.COST_WARNING_THRESHOLD:.2f}")
            
            # 检查每日使用量
            daily_limit = Config.MAX_COST_PER_DOCUMENT * 5  # 假设每日最多5个文档
            if today_used + estimated_cost > daily_limit:
                budget_status['warnings'].append(f"今日总成本将达到 ${today_used + estimated_cost:.2f}，建议控制使用量")
            
            # 生成建议
            if estimated_cost > Config.COST_WARNING_THRESHOLD:
                budget_status['recommendations'].append("考虑使用更经济的模型")
                budget_status['recommendations'].append("可以分批处理文档以控制成本")
            
            if len(budget_status['warnings']) == 0:
                budget_status['status'] = 'OK'
            elif budget_status['can_proceed']:
                budget_status['status'] = 'WARNING'
            else:
                budget_status['status'] = 'BLOCKED'
            
            return budget_status
            
        except Exception as e:
            logger.error(f"检查预算状态失败: {e}")
            return {
                'can_proceed': False,
                'error': str(e),
                'status': 'ERROR'
            }
    
    def suggest_cost_optimization(self, pages: List[Dict]) -> Dict:
        """
        建议成本优化方案
        
        Args:
            pages: 文档页面列表
            
        Returns:
            优化建议
        """
        try:
            suggestions = {
                'current_model': Config.DEFAULT_TRANSLATION_MODEL,
                'alternatives': [],
                'batch_processing': {},
                'content_optimization': []
            }
            
            # 比较不同模型的成本
            for model_name, pricing in MODEL_PRICING.items():
                if model_name != Config.DEFAULT_TRANSLATION_MODEL:
                    estimate = self.estimate_document_cost(pages, 'zh_to_en', model_name)
                    suggestions['alternatives'].append({
                        'model': model_name,
                        'estimated_cost': estimate.get('estimated_cost_with_buffer', 0),
                        'cost_savings': estimate.get('estimated_cost_with_buffer', 0) - 
                                      self.estimate_document_cost(pages, 'zh_to_en')['estimated_cost_with_buffer']
                    })
            
            # 排序替代方案
            suggestions['alternatives'].sort(key=lambda x: x['estimated_cost'])
            
            # 批处理建议
            if len(pages) > 10:
                batch_sizes = [5, 10, 20]
                for batch_size in batch_sizes:
                    num_batches = (len(pages) + batch_size - 1) // batch_size
                    suggestions['batch_processing'][f'batch_size_{batch_size}'] = {
                        'batches': num_batches,
                        'pages_per_batch': batch_size,
                        'estimated_time_per_batch': batch_size * 2  # 分钟
                    }
            
            # 内容优化建议
            long_pages = [p for p in pages if len(p.get('content', '')) > 5000]
            if long_pages:
                suggestions['content_optimization'].append(f"发现 {len(long_pages)} 页内容较长，可考虑分段处理")
            
            empty_pages = [p for p in pages if len(p.get('content', '').strip()) < 100]
            if empty_pages:
                suggestions['content_optimization'].append(f"发现 {len(empty_pages)} 页内容较少，可跳过处理")
            
            return suggestions
            
        except Exception as e:
            logger.error(f"生成优化建议失败: {e}")
            return {'error': str(e)}
