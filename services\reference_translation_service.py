"""
参考翻译服务 - 基于用户提供的参考文档进行翻译
"""

import os
import time
from typing import Dict, List, Optional, Tuple, Callable
import logging
import threading

from utils.reference_processor import ReferenceProcessor
from utils.simple_knowledge_base import SimpleKnowledgeBase
from utils.cost_estimator import CostEstimator
from utils.translation_history import TranslationHistory
from agents.translation_agent import TranslationAgent
from agents.review_agent import ReviewAgent
from config import Config

# 确保services目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)


class ReferenceTranslationService:
    """基于参考文档的翻译服务"""

    def __init__(self):
        self.doc_processor = ReferenceProcessor()
        self.knowledge_base = SimpleKnowledgeBase()
        self.cost_estimator = CostEstimator()
        self.history = TranslationHistory()
        self.translation_agent = TranslationAgent()
        self.review_agent = ReviewAgent()

        # 进度回调函数
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None

        # 线程锁
        self._lock = threading.Lock()

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback

    def _update_progress(self, current: int, total: int, message: str = ""):
        """更新进度"""
        if self.progress_callback:
            try:
                progress = current / total if total > 0 else 0
                self.progress_callback(progress, message)
            except Exception as e:
                logger.warning(f"进度回调失败: {e}")

    def _update_status(self, status: str):
        """更新状态"""
        if self.status_callback:
            try:
                self.status_callback(status)
            except Exception as e:
                logger.warning(f"状态回调失败: {e}")

    def add_reference_documents(self, reference_pairs: List[Tuple[str, str]]) -> Dict:
        """
        添加参考文档对

        Args:
            reference_pairs: 参考文档对列表 [(中文文档路径, 英文文档路径), ...]

        Returns:
            添加结果
        """
        try:
            self._update_status("添加参考文档...")

            results = []
            total_pairs = len(reference_pairs)

            for i, (zh_path, en_path) in enumerate(reference_pairs, 1):
                self._update_progress(
                    i * 100 // total_pairs, 100, f"处理参考文档 {i}/{total_pairs}..."
                )

                # 生成参考文档名称
                zh_name = os.path.basename(zh_path)
                en_name = os.path.basename(en_path)
                ref_name = f"{zh_name}__{en_name}"

                # 添加到知识库
                success = self.knowledge_base.add_reference_pair(
                    zh_path, en_path, ref_name
                )

                results.append(
                    {
                        "zh_file": zh_name,
                        "en_file": en_name,
                        "reference_name": ref_name,
                        "success": success,
                    }
                )

            successful_count = sum(1 for r in results if r["success"])

            self._update_status(
                f"参考文档添加完成: {successful_count}/{total_pairs} 成功"
            )

            return {
                "success": True,
                "total_pairs": total_pairs,
                "successful_pairs": successful_count,
                "results": results,
                "knowledge_base_stats": self.knowledge_base.get_reference_statistics(),
            }

        except Exception as e:
            logger.error(f"添加参考文档失败: {e}")
            return {"success": False, "error": str(e)}

    def translate_document(
        self, file_path: str, translation_direction: str, output_path: str = None
    ) -> Dict:
        """
        翻译文档主流程

        Args:
            file_path: 输入文件路径
            translation_direction: 翻译方向
            output_path: 输出文件路径

        Returns:
            翻译结果
        """
        try:
            start_time = time.time()
            self._update_status("开始翻译流程...")

            # 1. 验证文档
            self._update_progress(0, 100, "验证文档...")
            is_valid, error_msg = self.doc_processor.validate_document(file_path)
            if not is_valid:
                return {"success": False, "error": error_msg}

            # 2. 提取基本信息
            self._update_progress(5, 100, "分析文档信息...")
            basic_info = self.doc_processor.extract_basic_info(file_path)

            # 3. 分割文档
            self._update_progress(10, 100, "分析文档结构...")
            pages = self.doc_processor.split_document_by_pages(file_path)
            if not pages:
                return {"success": False, "error": "文档分割失败"}

            # 4. 成本估算
            self._update_progress(15, 100, "估算翻译成本...")
            cost_estimate = self.cost_estimator.estimate_document_cost(
                pages, translation_direction
            )

            # 检查预算
            budget_status = self.cost_estimator.check_budget_status(
                cost_estimate.get("estimated_cost_with_buffer", 0)
            )

            if not budget_status.get("can_proceed", False):
                return {
                    "success": False,
                    "error": "预算超限",
                    "budget_status": budget_status,
                    "cost_estimate": cost_estimate,
                }

            # 5. 创建翻译项目
            project_id = self.history.create_project(
                basic_info, translation_direction, file_path, len(pages)
            )

            # 6. 检查知识库状态
            self._update_progress(20, 100, "检查参考知识库...")
            kb_stats = self.knowledge_base.get_reference_statistics()
            knowledge_ready = kb_stats.get("reference_count", 0) > 0

            # 7. 执行翻译
            self._update_progress(25, 100, "开始翻译...")
            translation_results = self._translate_pages(
                pages, basic_info, translation_direction, project_id
            )

            # 8. 生成输出文档
            self._update_progress(90, 100, "生成输出文档...")
            if not output_path:
                output_path = self._generate_output_path(
                    file_path, translation_direction
                )

            output_file = self.doc_processor.create_translated_document(
                file_path, translation_results, output_path
            )

            # 9. 计算实际成本
            cost_tracking = self.cost_estimator.track_actual_cost(
                translation_results, basic_info
            )

            # 10. 更新项目状态
            total_cost = cost_tracking.get("total_cost", 0)
            processing_time = time.time() - start_time

            self.history.update_project_status(
                project_id, "completed", output_file, total_cost, processing_time
            )

            # 11. 生成质量报告
            quality_report = self.review_agent.final_quality_check(translation_results)

            self._update_progress(100, 100, "翻译完成!")

            return {
                "success": True,
                "project_id": project_id,
                "output_file": output_file,
                "basic_info": basic_info,
                "translation_results": translation_results,
                "cost_estimate": cost_estimate,
                "actual_cost": total_cost,
                "processing_time": processing_time,
                "quality_report": quality_report,
                "knowledge_base_ready": knowledge_ready,
                "knowledge_base_stats": kb_stats,
            }

        except Exception as e:
            logger.error(f"翻译流程失败: {e}")
            self._update_status(f"翻译失败: {str(e)}")
            return {"success": False, "error": str(e)}

    def _translate_pages(
        self,
        pages: List[Dict],
        basic_info: Dict,
        translation_direction: str,
        project_id: str,
    ) -> List[Dict]:
        """翻译所有页面"""
        try:
            translated_pages = []
            total_pages = len(pages)

            for i, page in enumerate(pages, 1):
                self._update_progress(
                    25 + int(60 * i / total_pages),
                    100,
                    f"翻译第 {i}/{total_pages} 页...",
                )

                # 翻译单页
                page_result = self._translate_single_page(
                    page, basic_info, translation_direction, project_id
                )

                translated_pages.append(page_result)

                # 保存页面结果
                self.history.save_page_translation(project_id, page_result)

            return translated_pages

        except Exception as e:
            logger.error(f"页面翻译失败: {e}")
            raise

    def _translate_single_page(
        self, page: Dict, basic_info: Dict, translation_direction: str, project_id: str
    ) -> Dict:
        """翻译单个页面（使用参考知识库）"""
        try:
            # 1. 获取参考翻译
            target_language = "en" if translation_direction == "zh_to_en" else "zh"
            reference_translations = self.knowledge_base.search_similar_content(
                page.get("content", ""), target_language
            )

            # 2. 搜索术语翻译
            source_language = "zh" if translation_direction == "zh_to_en" else "en"
            terminology_refs = self.knowledge_base.search_terminology(
                page.get("content", ""), source_language
            )

            # 3. 初始翻译
            translation_result = self.translation_agent.translate_page(
                page, reference_translations, translation_direction, basic_info
            )

            if not translation_result.get("success", False):
                return translation_result

            # 4. 校对循环（最多5次）
            revision_count = 0
            max_revisions = Config.MAX_RETRY_ATTEMPTS

            while revision_count < max_revisions:
                # 校对翻译
                review_result = self.review_agent.review_translation(
                    translation_result,
                    reference_translations,
                    basic_info,
                    translation_direction,
                )

                # 更新翻译结果
                translation_result.update(
                    {
                        "review_score": review_result.get("review_score", 0),
                        "consistency_score": review_result.get("consistency_score", 0),
                        "accuracy_score": review_result.get("accuracy_score", 0),
                        "issues_found": review_result.get("issues_found", []),
                        "suggestions": review_result.get("suggestions", []),
                        "terminology_used": len(terminology_refs),
                    }
                )

                # 检查是否需要修订
                if not review_result.get("needs_revision", False):
                    logger.info(f"第 {page.get('page_number', '?')} 页校对通过")
                    break

                # 生成修订反馈
                feedback = self.review_agent.generate_revision_feedback(
                    review_result, reference_translations
                )

                # 修订翻译
                translation_result = self.translation_agent.revise_translation(
                    translation_result, feedback, reference_translations
                )

                revision_count += 1
                logger.info(
                    f"第 {page.get('page_number', '?')} 页第 {revision_count} 次修订完成"
                )

            # 记录修订次数和参考使用情况
            translation_result["revision_count"] = revision_count
            translation_result["needs_revision"] = revision_count >= max_revisions
            translation_result["reference_count"] = len(reference_translations)

            return translation_result

        except Exception as e:
            logger.error(f"单页翻译失败: {e}")
            return {
                "page_number": page.get("page_number"),
                "success": False,
                "error": str(e),
            }

    def _generate_output_path(self, input_path: str, translation_direction: str) -> str:
        """生成输出文件路径"""
        try:
            input_file = os.path.basename(input_path)
            name, ext = os.path.splitext(input_file)

            direction_suffix = (
                "zh_to_en" if translation_direction == "zh_to_en" else "en_to_zh"
            )
            timestamp = time.strftime("%Y%m%d_%H%M%S")

            output_name = f"{name}_{direction_suffix}_{timestamp}{ext}"
            output_path = os.path.join(Config.TRANSLATION_HISTORY_DIR, output_name)

            return output_path

        except Exception as e:
            logger.error(f"生成输出路径失败: {e}")
            return f"translated_{int(time.time())}.docx"

    def get_translation_preview(self, file_path: str) -> Dict:
        """
        获取翻译预览信息

        Args:
            file_path: 文件路径

        Returns:
            预览信息
        """
        try:
            # 验证文档
            is_valid, error_msg = self.doc_processor.validate_document(file_path)
            if not is_valid:
                return {"success": False, "error": error_msg}

            # 提取基本信息
            basic_info = self.doc_processor.extract_basic_info(file_path)
            pages = self.doc_processor.split_document_by_pages(file_path)

            # 成本估算
            cost_estimate_zh_en = self.cost_estimator.estimate_document_cost(
                pages, "zh_to_en"
            )
            cost_estimate_en_zh = self.cost_estimator.estimate_document_cost(
                pages, "en_to_zh"
            )

            # 知识库统计
            kb_stats = self.knowledge_base.get_reference_statistics()

            preview = {
                "success": True,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_size_mb": os.path.getsize(file_path) / (1024 * 1024),
                    "total_pages": len(pages),
                    "estimated_content_length": sum(
                        len(p.get("content", "")) for p in pages
                    ),
                    "detected_language": basic_info.get("language", "unknown"),
                },
                "basic_info": basic_info,
                "cost_estimates": {
                    "zh_to_en": cost_estimate_zh_en,
                    "en_to_zh": cost_estimate_en_zh,
                },
                "knowledge_base_stats": kb_stats,
                "sample_content": (
                    pages[0].get("content", "")[:500] + "..." if pages else ""
                ),
            }

            return preview

        except Exception as e:
            logger.error(f"获取预览失败: {e}")
            return {"success": False, "error": str(e)}

    def get_knowledge_base_info(self) -> Dict:
        """获取知识库信息"""
        try:
            stats = self.knowledge_base.get_reference_statistics()
            references = self.knowledge_base.list_references()

            return {"success": True, "statistics": stats, "references": references}

        except Exception as e:
            logger.error(f"获取知识库信息失败: {e}")
            return {"success": False, "error": str(e)}

    def remove_reference_document(self, reference_id: str) -> Dict:
        """删除参考文档"""
        try:
            success = self.knowledge_base.remove_reference(reference_id)

            if success:
                return {
                    "success": True,
                    "message": f"参考文档 {reference_id} 已删除",
                    "updated_stats": self.knowledge_base.get_reference_statistics(),
                }
            else:
                return {"success": False, "error": "删除失败"}

        except Exception as e:
            logger.error(f"删除参考文档失败: {e}")
            return {"success": False, "error": str(e)}

    def get_translation_preview(self, file_path: str) -> Dict:
        """
        获取翻译预览信息

        Args:
            file_path: 文件路径

        Returns:
            预览信息
        """
        try:
            # 验证文档
            is_valid, error_msg = self.doc_processor.validate_document(file_path)
            if not is_valid:
                return {"success": False, "error": error_msg}

            # 提取基本信息
            basic_info = self.doc_processor.extract_basic_info(file_path)
            pages = self.doc_processor.split_document_by_pages(file_path)

            # 成本估算
            cost_estimate_zh_en = self.cost_estimator.estimate_document_cost(
                pages, "zh_to_en"
            )
            cost_estimate_en_zh = self.cost_estimator.estimate_document_cost(
                pages, "en_to_zh"
            )

            # 知识库统计
            kb_stats = self.knowledge_base.get_reference_statistics()

            preview = {
                "success": True,
                "file_info": {
                    "file_name": os.path.basename(file_path),
                    "file_size_mb": os.path.getsize(file_path) / (1024 * 1024),
                    "total_pages": len(pages),
                    "estimated_content_length": sum(
                        len(p.get("content", "")) for p in pages
                    ),
                    "detected_language": basic_info.get("language", "unknown"),
                },
                "basic_info": basic_info,
                "cost_estimates": {
                    "zh_to_en": cost_estimate_zh_en,
                    "en_to_zh": cost_estimate_en_zh,
                },
                "knowledge_base_stats": kb_stats,
                "sample_content": (
                    pages[0].get("content", "")[:500] + "..." if pages else ""
                ),
            }

            return preview

        except Exception as e:
            logger.error(f"获取预览失败: {e}")
            return {"success": False, "error": str(e)}
