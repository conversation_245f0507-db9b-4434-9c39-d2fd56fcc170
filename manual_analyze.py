#!/usr/bin/env python3
"""
手动分析文档内容
"""
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    try:
        from docx import Document
        import re
        
        print("开始分析 2022_ar_e.docx...")
        
        # 打开文档
        doc = Document("2022_ar_e.docx")
        
        print(f"文档基本信息:")
        print(f"  段落数: {len(doc.paragraphs)}")
        print(f"  表格数: {len(doc.tables)}")
        
        # 收集前50段的文本
        text_parts = []
        for i, p in enumerate(doc.paragraphs[:50]):
            text = p.text.strip()
            if text:
                text_parts.append(f"段落{i+1}: {text}")
        
        # 收集前3个表格的文本
        for table_idx, table in enumerate(doc.tables[:3]):
            for row_idx, row in enumerate(table.rows[:5]):
                cells = [cell.text.strip() for cell in row.cells if cell.text.strip()]
                if cells:
                    text_parts.append(f"表格{table_idx+1}行{row_idx+1}: {' | '.join(cells)}")
        
        # 显示前20个文本片段
        print("\n前20个文本片段:")
        for i, text in enumerate(text_parts[:20]):
            print(f"{i+1:2d}. {text[:100]}{'...' if len(text) > 100 else ''}")
        
        # 合并所有文本用于模式匹配
        all_text = "\n".join(text_parts)
        
        print(f"\n模式匹配结果:")
        
        # 搜索股票代码
        stock_patterns = [
            r"股票代码[：:\s]*(\d{4,5})",
            r"Stock Code[：:\s]*(\d{4,5})",
            r"代码[：:\s]*(\d{4,5})",
            r"(\d{4})\.HK",
            r"HK(\d{4})",
        ]
        
        print("股票代码搜索:")
        for pattern in stock_patterns:
            matches = re.findall(pattern, all_text, re.IGNORECASE)
            if matches:
                print(f"  {pattern}: {matches}")
        
        # 搜索所有4位数字
        all_4digit = re.findall(r'\b(\d{4})\b', all_text)
        if all_4digit:
            print(f"  所有4位数字: {list(set(all_4digit))}")
        
        # 搜索公司名称
        print("\n公司名称搜索:")
        name_patterns = [
            r"([^，。\n]{3,50}(?:有限公司|Limited|Ltd))",
            r"([^，。\n]{3,30}(?:集团|控股|Holdings|Group))",
        ]
        
        for pattern in name_patterns:
            matches = re.findall(pattern, all_text)
            if matches:
                unique = list(set(matches))[:3]
                print(f"  {pattern}: {unique}")
        
        # 搜索年份
        print("\n年份搜索:")
        year_patterns = [
            r"(\d{4})年",
            r"Year (\d{4})",
            r"20\d{2}",
        ]
        
        for pattern in year_patterns:
            matches = re.findall(pattern, all_text)
            if matches:
                unique = sorted(list(set(matches)))
                print(f"  {pattern}: {unique}")
        
        # 使用现有的文档处理器
        print(f"\n使用文档处理器提取:")
        from utils.document_processor import DocumentProcessor
        processor = DocumentProcessor()
        result = processor.extract_company_info("2022_ar_e.docx")
        
        for key, value in result.items():
            print(f"  {key}: '{value}'")
        
        print("\n分析完成!")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
