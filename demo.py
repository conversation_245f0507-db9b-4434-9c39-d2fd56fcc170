#!/usr/bin/env python3
"""
演示脚本 - 展示系统核心功能
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_demo_document():
    """创建演示文档"""
    print("📄 创建演示文档...")
    
    try:
        from docx import Document
        
        # 创建演示文档
        doc = Document()
        
        # 添加标题
        doc.add_heading('腾讯控股有限公司', 0)
        doc.add_heading('2023年年度报告', 1)
        
        # 添加公司信息
        doc.add_paragraph('公司名称：腾讯控股有限公司')
        doc.add_paragraph('英文名称：Tencent Holdings Limited')
        doc.add_paragraph('股票代码：0700')
        doc.add_paragraph('报告类型：年报')
        doc.add_paragraph('报告年份：2023')
        
        # 添加业务概述
        doc.add_heading('业务概述', 2)
        doc.add_paragraph('''
腾讯是一家世界领先的互联网科技公司，致力于通过技术丰富人们的数字生活。
我们的使命是"用户为本，科技向善"。腾讯把"连接一切"作为战略目标，
提供社交平台与数字内容、金融科技及企业服务、网络广告等服务。
        ''')
        
        # 添加财务数据表格
        doc.add_heading('主要财务数据', 2)
        table = doc.add_table(rows=4, cols=3)
        table.style = 'Table Grid'
        
        # 表头
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = '项目'
        hdr_cells[1].text = '2023年'
        hdr_cells[2].text = '2022年'
        
        # 数据行
        row1_cells = table.rows[1].cells
        row1_cells[0].text = '营业收入（人民币百万元）'
        row1_cells[1].text = '609,811'
        row1_cells[2].text = '554,552'
        
        row2_cells = table.rows[2].cells
        row2_cells[0].text = '净利润（人民币百万元）'
        row2_cells[1].text = '125,395'
        row2_cells[2].text = '127,446'
        
        row3_cells = table.rows[3].cells
        row3_cells[0].text = '每股基本盈利（人民币元）'
        row3_cells[1].text = '13.23'
        row3_cells[2].text = '13.47'
        
        # 添加业务分析
        doc.add_heading('业务分析', 2)
        doc.add_paragraph('''
2023年，腾讯继续专注于高质量发展，在各个业务领域都取得了显著进展。
增值服务业务保持稳健增长，网络广告业务逐步复苏，
金融科技及企业服务业务持续扩展。
        ''')
        
        # 添加风险因素
        doc.add_heading('风险因素', 2)
        doc.add_paragraph('''
公司面临的主要风险包括：监管政策变化、市场竞争加剧、
技术发展不确定性、网络安全风险等。公司将持续关注
并采取相应措施应对这些风险。
        ''')
        
        # 保存文档
        demo_file = Path("demo_tencent_annual_report.docx")
        doc.save(demo_file)
        
        print(f"✅ 演示文档已创建: {demo_file}")
        return str(demo_file)
        
    except Exception as e:
        print(f"❌ 创建演示文档失败: {e}")
        return None

def demo_document_processing():
    """演示文档处理功能"""
    print("\n🔍 演示文档处理功能...")
    
    try:
        from utils.document_processor import DocumentProcessor
        
        # 创建演示文档
        demo_file = create_demo_document()
        if not demo_file:
            return False
        
        processor = DocumentProcessor()
        
        # 提取公司信息
        print("📊 提取公司信息...")
        company_info = processor.extract_company_info(demo_file)
        print(f"   公司名称（中文）: {company_info.get('company_name_zh', '未识别')}")
        print(f"   公司名称（英文）: {company_info.get('company_name_en', '未识别')}")
        print(f"   股票代码: {company_info.get('stock_code', '未识别')}")
        print(f"   报告类型: {company_info.get('report_type', '未识别')}")
        
        # 分割文档
        print("📄 分割文档...")
        pages = processor.split_document_by_pages(demo_file)
        print(f"   共分割为 {len(pages)} 页")
        
        # 显示第一页内容预览
        if pages:
            first_page = pages[0]
            content_preview = first_page.get('content', '')[:200] + '...'
            print(f"   第一页内容预览: {content_preview}")
        
        print("✅ 文档处理演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 文档处理演示失败: {e}")
        return False

def demo_cost_estimation():
    """演示成本估算功能"""
    print("\n💰 演示成本估算功能...")
    
    try:
        from utils.cost_estimator import CostEstimator
        from utils.document_processor import DocumentProcessor
        
        # 创建演示文档
        demo_file = create_demo_document()
        if not demo_file:
            return False
        
        processor = DocumentProcessor()
        estimator = CostEstimator()
        
        # 分割文档
        pages = processor.split_document_by_pages(demo_file)
        
        # 估算中文到英文翻译成本
        print("📊 估算中文→英文翻译成本...")
        cost_zh_en = estimator.estimate_document_cost(pages, 'zh_to_en')
        print(f"   预估成本: ${cost_zh_en.get('estimated_cost_with_buffer', 0):.4f}")
        print(f"   预估时间: {cost_zh_en.get('estimated_time_minutes', 0)} 分钟")
        print(f"   每页成本: ${cost_zh_en.get('cost_per_page', 0):.4f}")
        
        # 估算英文到中文翻译成本
        print("📊 估算英文→中文翻译成本...")
        cost_en_zh = estimator.estimate_document_cost(pages, 'en_to_zh')
        print(f"   预估成本: ${cost_en_zh.get('estimated_cost_with_buffer', 0):.4f}")
        print(f"   预估时间: {cost_en_zh.get('estimated_time_minutes', 0)} 分钟")
        print(f"   每页成本: ${cost_en_zh.get('cost_per_page', 0):.4f}")
        
        # 检查预算状态
        budget_status = estimator.check_budget_status(cost_zh_en.get('estimated_cost_with_buffer', 0))
        print(f"   预算状态: {budget_status.get('status', '未知')}")
        
        print("✅ 成本估算演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 成本估算演示失败: {e}")
        return False

def demo_knowledge_base():
    """演示知识库功能"""
    print("\n📚 演示知识库功能...")
    
    try:
        from utils.knowledge_base import KnowledgeBase
        
        kb = KnowledgeBase()
        
        # 获取知识库统计
        print("📊 知识库统计信息...")
        stats = kb.get_knowledge_base_stats()
        print(f"   总文档数: {stats.get('total_documents', 0)}")
        print(f"   中文文档数: {stats.get('chinese_documents', 0)}")
        print(f"   英文文档数: {stats.get('english_documents', 0)}")
        print(f"   公司数量: {stats.get('companies_count', 0)}")
        
        # 演示相似内容搜索
        print("🔍 演示相似内容搜索...")
        test_query = "营业收入增长"
        company_info = {'stock_code': '0700', 'company_name_zh': '腾讯控股'}
        
        similar_content = kb.search_similar_content(test_query, company_info, 'zh', top_k=3)
        print(f"   查询: {test_query}")
        print(f"   找到 {len(similar_content)} 个相似内容")
        
        for i, content in enumerate(similar_content[:2], 1):
            similarity = content.get('similarity_score', 0)
            preview = content.get('content', '')[:100] + '...'
            print(f"   结果 {i} (相似度: {similarity:.2f}): {preview}")
        
        print("✅ 知识库演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 知识库演示失败: {e}")
        return False

def demo_translation_preview():
    """演示翻译预览功能"""
    print("\n👀 演示翻译预览功能...")
    
    try:
        from services.translation_service import TranslationService
        
        # 创建演示文档
        demo_file = create_demo_document()
        if not demo_file:
            return False
        
        service = TranslationService()
        
        # 获取翻译预览
        print("📋 获取翻译预览...")
        preview = service.get_translation_preview(demo_file)
        
        if preview.get('success', False):
            file_info = preview['file_info']
            company_info = preview['company_info']
            cost_estimates = preview['cost_estimates']
            
            print("📄 文件信息:")
            print(f"   文件名: {file_info['file_name']}")
            print(f"   文件大小: {file_info['file_size_mb']:.1f} MB")
            print(f"   总页数: {file_info['total_pages']}")
            
            print("🏢 公司信息:")
            print(f"   中文名称: {company_info.get('company_name_zh', '未识别')}")
            print(f"   英文名称: {company_info.get('company_name_en', '未识别')}")
            print(f"   股票代码: {company_info.get('stock_code', '未识别')}")
            
            print("💰 成本估算:")
            zh_en_cost = cost_estimates['zh_to_en'].get('estimated_cost_with_buffer', 0)
            en_zh_cost = cost_estimates['en_to_zh'].get('estimated_cost_with_buffer', 0)
            print(f"   中文→英文: ${zh_en_cost:.4f}")
            print(f"   英文→中文: ${en_zh_cost:.4f}")
            
            print("✅ 翻译预览演示完成")
            return True
        else:
            print(f"❌ 翻译预览失败: {preview.get('error', '未知错误')}")
            return False
        
    except Exception as e:
        print(f"❌ 翻译预览演示失败: {e}")
        return False

def cleanup_demo_files():
    """清理演示文件"""
    print("\n🧹 清理演示文件...")
    
    demo_files = [
        "demo_tencent_annual_report.docx"
    ]
    
    for file_path in demo_files:
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"   已删除: {file_path}")

def main():
    """主演示函数"""
    print("🎭 港交所上市公司披露报告翻译系统 - 功能演示")
    print("=" * 60)
    
    demos = [
        ("文档处理功能", demo_document_processing),
        ("成本估算功能", demo_cost_estimation),
        ("知识库功能", demo_knowledge_base),
        ("翻译预览功能", demo_translation_preview)
    ]
    
    success_count = 0
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                success_count += 1
            else:
                print(f"⚠️  {demo_name}演示未完全成功")
        except Exception as e:
            print(f"❌ {demo_name}演示失败: {e}")
    
    # 清理演示文件
    cleanup_demo_files()
    
    print("\n" + "=" * 60)
    print(f"📊 演示结果: {success_count}/{len(demos)} 个功能演示成功")
    
    if success_count == len(demos):
        print("🎉 所有功能演示成功！系统运行正常。")
        print("\n🚀 现在可以启动完整系统:")
        print("   python start.py")
    elif success_count >= len(demos) - 1:
        print("✅ 核心功能演示成功！系统基本可用。")
        print("⚠️  部分功能可能需要进一步配置。")
    else:
        print("❌ 多个功能演示失败，请检查系统配置。")
        print("💡 建议运行: python test_system.py")

if __name__ == "__main__":
    main()
