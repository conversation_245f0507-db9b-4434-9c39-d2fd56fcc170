#!/usr/bin/env python3
"""
港交所上市公司披露报告翻译系统启动脚本
"""
import os
import sys
import logging
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")

def check_environment():
    """检查环境配置"""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ 未找到.env文件，请复制.env.example并配置")
        print("   cp .env.example .env")
        print("   然后编辑.env文件，设置OPENROUTER_API_KEY等配置")
        sys.exit(1)
    
    # 检查关键配置
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ 请在.env文件中设置OPENROUTER_API_KEY")
        sys.exit(1)
    
    print("✅ 环境配置检查通过")

def install_dependencies():
    """安装依赖"""
    print("📦 检查并安装依赖...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        sys.exit(1)

def setup_directories():
    """创建必要的目录"""
    directories = [
        "temp",
        "knowledge_base", 
        "translation_history",
        "chroma_db"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录结构创建完成")

def check_chrome_driver():
    """检查Chrome WebDriver"""
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.quit()
        print("✅ Chrome WebDriver 可用")
        return True
    except Exception as e:
        print(f"⚠️  Chrome WebDriver 不可用: {e}")
        print("   网页爬虫功能将受限，但翻译功能仍可正常使用")
        return False

def main():
    """主函数"""
    print("🚀 启动港交所上市公司披露报告翻译系统...")
    print("=" * 60)
    
    # 检查Python版本
    check_python_version()
    
    # 安装依赖
    install_dependencies()
    
    # 检查环境配置
    check_environment()
    
    # 创建目录
    setup_directories()
    
    # 检查Chrome Driver
    check_chrome_driver()
    
    print("=" * 60)
    print("✅ 系统初始化完成，启动Gradio界面...")
    
    # 启动应用
    try:
        from app import create_interface
        app = create_interface()
        
        print("\n🌐 系统已启动，请在浏览器中访问:")
        print("   本地访问: http://localhost:7860")
        print("   局域网访问: http://0.0.0.0:7860")
        print("\n💡 使用说明:")
        print("   1. 上传DOCX格式的披露报告")
        print("   2. 选择翻译方向（中→英 或 英→中）")
        print("   3. 点击预览查看文档信息和成本估算")
        print("   4. 确认后开始翻译")
        print("   5. 翻译完成后下载结果文件")
        print("\n⚠️  注意事项:")
        print("   - 首次使用会自动下载AI模型，可能需要较长时间")
        print("   - 翻译质量依赖于知识库，建议先处理几个同类报告")
        print("   - 大文档翻译可能需要较长时间和较高成本")
        print("\n按 Ctrl+C 停止服务")
        
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            max_file_size="50mb"
        )
        
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
