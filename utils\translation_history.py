"""
翻译历史管理模块 - 保存和管理翻译历史记录
"""

import os
import json
import sqlite3
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import hashlib
import logging
from config import Config

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)


class TranslationHistory:
    """翻译历史管理器"""

    def __init__(self):
        self.db_path = Config.TRANSLATION_HISTORY_DB
        self._init_database()

    def _init_database(self):
        """初始化数据库"""
        try:
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 创建翻译项目表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS translation_projects (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_id TEXT UNIQUE NOT NULL,
                        company_name_zh TEXT,
                        company_name_en TEXT,
                        stock_code TEXT,
                        report_type TEXT,
                        report_year TEXT,
                        translation_direction TEXT,
                        total_pages INTEGER,
                        total_cost REAL,
                        processing_time REAL,
                        status TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        completed_at TIMESTAMP,
                        file_path TEXT,
                        output_path TEXT
                    )
                """
                )

                # 创建翻译页面表
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS translation_pages (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_id TEXT NOT NULL,
                        page_number INTEGER NOT NULL,
                        original_content TEXT,
                        translated_content TEXT,
                        review_score REAL,
                        revision_count INTEGER DEFAULT 0,
                        cost REAL,
                        processing_time REAL,
                        model_used TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (project_id) REFERENCES translation_projects (project_id)
                    )
                """
                )

                # 创建翻译版本表（用于版本管理）
                cursor.execute(
                    """
                    CREATE TABLE IF NOT EXISTS translation_versions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        project_id TEXT NOT NULL,
                        version_number INTEGER NOT NULL,
                        description TEXT,
                        file_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (project_id) REFERENCES translation_projects (project_id)
                    )
                """
                )

                # 创建索引
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_project_id ON translation_pages (project_id)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_stock_code ON translation_projects (stock_code)"
                )
                cursor.execute(
                    "CREATE INDEX IF NOT EXISTS idx_created_at ON translation_projects (created_at)"
                )

                conn.commit()
                logger.info("翻译历史数据库初始化完成")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    def create_project(
        self,
        company_info: Dict,
        translation_direction: str,
        file_path: str,
        total_pages: int,
    ) -> str:
        """
        创建翻译项目

        Args:
            company_info: 公司信息
            translation_direction: 翻译方向
            file_path: 源文件路径
            total_pages: 总页数

        Returns:
            项目ID
        """
        try:
            # 生成项目ID
            project_data = f"{company_info.get('stock_code', '')}-{translation_direction}-{datetime.now().isoformat()}"
            project_id = hashlib.md5(project_data.encode()).hexdigest()[:16]

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO translation_projects
                    (project_id, company_name_zh, company_name_en, stock_code,
                     report_type, report_year, translation_direction, total_pages,
                     status, file_path)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        project_id,
                        company_info.get("company_name_zh", ""),
                        company_info.get("company_name_en", ""),
                        company_info.get("stock_code", ""),
                        company_info.get("report_type", ""),
                        company_info.get("report_year", ""),
                        translation_direction,
                        total_pages,
                        "in_progress",
                        file_path,
                    ),
                )
                conn.commit()

            logger.info(f"创建翻译项目: {project_id}")
            return project_id

        except Exception as e:
            logger.error(f"创建翻译项目失败: {e}")
            raise

    def save_page_translation(self, project_id: str, page_result: Dict):
        """
        保存页面翻译结果

        Args:
            project_id: 项目ID
            page_result: 页面翻译结果
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO translation_pages
                    (project_id, page_number, original_content, translated_content,
                     review_score, revision_count, cost, processing_time, model_used)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        project_id,
                        page_result.get("page_number", 0),
                        page_result.get("original_content", ""),
                        page_result.get("translated_content", ""),
                        page_result.get("review_score", 0),
                        page_result.get("revision_count", 0),
                        page_result.get("cost", 0),
                        page_result.get("processing_time", 0),
                        page_result.get("model_used", ""),
                    ),
                )
                conn.commit()

        except Exception as e:
            logger.error(f"保存页面翻译失败: {e}")

    def update_project_status(
        self,
        project_id: str,
        status: str,
        output_path: str = None,
        total_cost: float = None,
        processing_time: float = None,
    ):
        """
        更新项目状态

        Args:
            project_id: 项目ID
            status: 状态
            output_path: 输出文件路径
            total_cost: 总成本
            processing_time: 处理时间
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                update_fields = ["status = ?"]
                values = [status]

                if output_path:
                    update_fields.append("output_path = ?")
                    values.append(output_path)

                if total_cost is not None:
                    update_fields.append("total_cost = ?")
                    values.append(total_cost)

                if processing_time is not None:
                    update_fields.append("processing_time = ?")
                    values.append(processing_time)

                if status == "completed":
                    update_fields.append("completed_at = ?")
                    values.append(datetime.now().isoformat())

                values.append(project_id)

                cursor.execute(
                    f"""
                    UPDATE translation_projects
                    SET {', '.join(update_fields)}
                    WHERE project_id = ?
                """,
                    values,
                )
                conn.commit()

        except Exception as e:
            logger.error(f"更新项目状态失败: {e}")

    def save_version(
        self, project_id: str, version_number: int, description: str, file_path: str
    ):
        """
        保存翻译版本

        Args:
            project_id: 项目ID
            version_number: 版本号
            description: 版本描述
            file_path: 文件路径
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO translation_versions
                    (project_id, version_number, description, file_path)
                    VALUES (?, ?, ?, ?)
                """,
                    (project_id, version_number, description, file_path),
                )
                conn.commit()

        except Exception as e:
            logger.error(f"保存版本失败: {e}")

    def get_project_history(self, limit: int = 50) -> List[Dict]:
        """
        获取项目历史

        Args:
            limit: 返回数量限制

        Returns:
            项目历史列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT project_id, company_name_zh, company_name_en, stock_code,
                           report_type, translation_direction, total_pages, total_cost,
                           status, created_at, completed_at, file_path, output_path
                    FROM translation_projects
                    ORDER BY created_at DESC
                    LIMIT ?
                """,
                    (limit,),
                )

                columns = [desc[0] for desc in cursor.description]
                projects = []

                for row in cursor.fetchall():
                    project = dict(zip(columns, row))
                    projects.append(project)

                return projects

        except Exception as e:
            logger.error(f"获取项目历史失败: {e}")
            return []

    def get_project_details(self, project_id: str) -> Optional[Dict]:
        """
        获取项目详情

        Args:
            project_id: 项目ID

        Returns:
            项目详情
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取项目基本信息
                cursor.execute(
                    """
                    SELECT * FROM translation_projects WHERE project_id = ?
                """,
                    (project_id,),
                )

                project_row = cursor.fetchone()
                if not project_row:
                    return None

                columns = [desc[0] for desc in cursor.description]
                project = dict(zip(columns, project_row))

                # 获取页面翻译信息
                cursor.execute(
                    """
                    SELECT page_number, review_score, revision_count, cost,
                           processing_time, model_used, created_at
                    FROM translation_pages
                    WHERE project_id = ?
                    ORDER BY page_number
                """,
                    (project_id,),
                )

                page_columns = [desc[0] for desc in cursor.description]
                pages = []
                for row in cursor.fetchall():
                    page = dict(zip(page_columns, row))
                    pages.append(page)

                project["pages"] = pages

                # 获取版本信息
                cursor.execute(
                    """
                    SELECT version_number, description, file_path, created_at
                    FROM translation_versions
                    WHERE project_id = ?
                    ORDER BY version_number DESC
                """,
                    (project_id,),
                )

                version_columns = [desc[0] for desc in cursor.description]
                versions = []
                for row in cursor.fetchall():
                    version = dict(zip(version_columns, row))
                    versions.append(version)

                project["versions"] = versions

                return project

        except Exception as e:
            logger.error(f"获取项目详情失败: {e}")
            return None

    def search_projects(
        self,
        stock_code: str = None,
        report_type: str = None,
        translation_direction: str = None,
        status: str = None,
    ) -> List[Dict]:
        """
        搜索项目

        Args:
            stock_code: 股票代码
            report_type: 报告类型
            translation_direction: 翻译方向
            status: 状态

        Returns:
            匹配的项目列表
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                conditions = []
                values = []

                if stock_code:
                    conditions.append("stock_code = ?")
                    values.append(stock_code)

                if report_type:
                    conditions.append("report_type = ?")
                    values.append(report_type)

                if translation_direction:
                    conditions.append("translation_direction = ?")
                    values.append(translation_direction)

                if status:
                    conditions.append("status = ?")
                    values.append(status)

                where_clause = " AND ".join(conditions) if conditions else "1=1"

                cursor.execute(
                    f"""
                    SELECT project_id, company_name_zh, company_name_en, stock_code,
                           report_type, translation_direction, total_pages, total_cost,
                           status, created_at, completed_at
                    FROM translation_projects
                    WHERE {where_clause}
                    ORDER BY created_at DESC
                """,
                    values,
                )

                columns = [desc[0] for desc in cursor.description]
                projects = []

                for row in cursor.fetchall():
                    project = dict(zip(columns, row))
                    projects.append(project)

                return projects

        except Exception as e:
            logger.error(f"搜索项目失败: {e}")
            return []

    def get_statistics(self) -> Dict:
        """
        获取统计信息

        Returns:
            统计信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 总项目数
                cursor.execute("SELECT COUNT(*) FROM translation_projects")
                total_projects = cursor.fetchone()[0]

                # 完成的项目数
                cursor.execute(
                    'SELECT COUNT(*) FROM translation_projects WHERE status = "completed"'
                )
                completed_projects = cursor.fetchone()[0]

                # 总成本
                cursor.execute(
                    "SELECT SUM(total_cost) FROM translation_projects WHERE total_cost IS NOT NULL"
                )
                total_cost = cursor.fetchone()[0] or 0

                # 总页数
                cursor.execute("SELECT SUM(total_pages) FROM translation_projects")
                total_pages = cursor.fetchone()[0] or 0

                # 平均评分
                cursor.execute(
                    "SELECT AVG(review_score) FROM translation_pages WHERE review_score > 0"
                )
                avg_review_score = cursor.fetchone()[0] or 0

                # 按公司统计
                cursor.execute(
                    """
                    SELECT stock_code, COUNT(*) as project_count
                    FROM translation_projects
                    WHERE stock_code IS NOT NULL AND stock_code != ""
                    GROUP BY stock_code
                    ORDER BY project_count DESC
                    LIMIT 10
                """
                )

                company_stats = []
                for row in cursor.fetchall():
                    company_stats.append(
                        {"stock_code": row[0], "project_count": row[1]}
                    )

                return {
                    "total_projects": total_projects,
                    "completed_projects": completed_projects,
                    "completion_rate": (
                        completed_projects / total_projects if total_projects > 0 else 0
                    ),
                    "total_cost": total_cost,
                    "total_pages": total_pages,
                    "average_cost_per_page": (
                        total_cost / total_pages if total_pages > 0 else 0
                    ),
                    "average_review_score": avg_review_score,
                    "top_companies": company_stats,
                }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def cleanup_old_records(self, days: int = 90):
        """
        清理旧记录

        Args:
            days: 保留天数
        """
        try:
            cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 删除旧的翻译页面记录
                cursor.execute(
                    """
                    DELETE FROM translation_pages
                    WHERE project_id IN (
                        SELECT project_id FROM translation_projects
                        WHERE created_at < ?
                    )
                """,
                    (cutoff_date,),
                )

                # 删除旧的版本记录
                cursor.execute(
                    """
                    DELETE FROM translation_versions
                    WHERE project_id IN (
                        SELECT project_id FROM translation_projects
                        WHERE created_at < ?
                    )
                """,
                    (cutoff_date,),
                )

                # 删除旧的项目记录
                cursor.execute(
                    """
                    DELETE FROM translation_projects
                    WHERE created_at < ?
                """,
                    (cutoff_date,),
                )

                deleted_count = cursor.rowcount
                conn.commit()

                logger.info(f"清理了 {deleted_count} 条旧记录")

        except Exception as e:
            logger.error(f"清理旧记录失败: {e}")
