#!/usr/bin/env python3
"""
简单分析文档
"""
try:
    from docx import Document
    import re
    
    doc = Document("2022_ar_e.docx")
    
    print("=== 文档分析 ===")
    print(f"段落数: {len(doc.paragraphs)}")
    print(f"表格数: {len(doc.tables)}")
    
    # 收集所有文本
    all_text = ""
    for p in doc.paragraphs[:50]:  # 前50段
        all_text += p.text + "\n"
    
    for table in doc.tables[:3]:  # 前3个表格
        for row in table.rows:
            for cell in row.cells:
                all_text += cell.text + "\n"
    
    print("\n=== 前10段非空内容 ===")
    count = 0
    for i, p in enumerate(doc.paragraphs):
        text = p.text.strip()
        if text and count < 10:
            print(f"{count+1}. {text}")
            count += 1
    
    print("\n=== 搜索股票代码 ===")
    patterns = [
        r"股票代码[：:\s]*(\d{4,5})",
        r"Stock Code[：:\s]*(\d{4,5})", 
        r"代码[：:\s]*(\d{4,5})",
        r"(\d{4})\.HK",
        r"HK(\d{4})",
        r"证券代码[：:\s]*(\d{4,5})",
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, all_text, re.IGNORECASE)
        if matches:
            print(f"模式 '{pattern}': {matches}")
    
    print("\n=== 搜索公司名称 ===")
    company_patterns = [
        r"([^，。\n]{3,50}(?:有限公司|Limited|Ltd|Corporation|Holdings|Group))",
    ]
    
    for pattern in company_patterns:
        matches = re.findall(pattern, all_text)
        if matches:
            unique = list(set(matches))[:5]
            print(f"公司名称: {unique}")
    
    print("\n=== 搜索年份 ===")
    year_matches = re.findall(r"20\d{2}", all_text)
    if year_matches:
        unique_years = sorted(list(set(year_matches)))
        print(f"年份: {unique_years}")
    
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()
