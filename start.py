#!/usr/bin/env python3
"""
简化启动脚本 - 快速启动翻译系统
"""
import os
import sys
from pathlib import Path

def main():
    """主函数"""
    print("🚀 港交所上市公司披露报告翻译系统")
    print("=" * 50)
    
    # 检查.env文件
    if not Path(".env").exists():
        print("❌ 未找到.env文件")
        print("\n请按以下步骤配置:")
        print("1. 复制配置文件: cp .env.example .env")
        print("2. 编辑.env文件，设置OPENROUTER_API_KEY")
        print("3. 重新运行此脚本")
        return
    
    # 检查API密钥
    try:
        from dotenv import load_dotenv
        load_dotenv()
        api_key = os.getenv("OPENROUTER_API_KEY")
        if not api_key:
            print("❌ 请在.env文件中设置OPENROUTER_API_KEY")
            return
    except ImportError:
        print("❌ 请先安装依赖: pip install python-dotenv")
        return
    
    print("✅ 配置检查通过")
    
    # 启动应用
    try:
        print("🌐 启动Gradio界面...")
        from app import create_interface
        
        app = create_interface()
        
        print("\n✅ 系统已启动!")
        print("📱 本地访问: http://localhost:7860")
        print("🌍 局域网访问: http://0.0.0.0:7860")
        print("\n按 Ctrl+C 停止服务")
        
        app.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            debug=False,
            show_error=True,
            max_file_size="50mb"
        )
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
    except KeyboardInterrupt:
        print("\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
