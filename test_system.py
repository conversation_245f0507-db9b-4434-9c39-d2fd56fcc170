#!/usr/bin/env python3
"""
系统测试脚本 - 验证各个模块的基本功能
"""
import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config():
    """测试配置模块"""
    print("🧪 测试配置模块...")
    try:
        from config import Config
        Config.validate_config()
        print("✅ 配置模块正常")
        return True
    except Exception as e:
        print(f"❌ 配置模块错误: {e}")
        return False

def test_document_processor():
    """测试文档处理模块"""
    print("🧪 测试文档处理模块...")
    try:
        from utils.document_processor import DocumentProcessor
        processor = DocumentProcessor()
        
        # 测试基本功能
        supported_formats = processor.supported_formats
        assert '.docx' in supported_formats
        
        print("✅ 文档处理模块正常")
        return True
    except Exception as e:
        print(f"❌ 文档处理模块错误: {e}")
        return False

def test_knowledge_base():
    """测试知识库模块"""
    print("🧪 测试知识库模块...")
    try:
        from utils.knowledge_base import KnowledgeBase
        kb = KnowledgeBase()
        
        # 测试基本功能
        stats = kb.get_knowledge_base_stats()
        assert isinstance(stats, dict)
        
        print("✅ 知识库模块正常")
        return True
    except Exception as e:
        print(f"❌ 知识库模块错误: {e}")
        return False

def test_cost_estimator():
    """测试成本估算模块"""
    print("🧪 测试成本估算模块...")
    try:
        from utils.cost_estimator import CostEstimator
        estimator = CostEstimator()
        
        # 测试基本功能
        test_pages = [{'content': '这是一个测试页面内容。'}]
        estimate = estimator.estimate_document_cost(test_pages, 'zh_to_en')
        assert isinstance(estimate, dict)
        assert 'estimated_cost' in estimate
        
        print("✅ 成本估算模块正常")
        return True
    except Exception as e:
        print(f"❌ 成本估算模块错误: {e}")
        return False

def test_translation_history():
    """测试翻译历史模块"""
    print("🧪 测试翻译历史模块...")
    try:
        from utils.translation_history import TranslationHistory
        history = TranslationHistory()
        
        # 测试基本功能
        stats = history.get_statistics()
        assert isinstance(stats, dict)
        
        print("✅ 翻译历史模块正常")
        return True
    except Exception as e:
        print(f"❌ 翻译历史模块错误: {e}")
        return False

def test_translation_agent():
    """测试翻译智能体"""
    print("🧪 测试翻译智能体...")
    try:
        from agents.translation_agent import TranslationAgent
        agent = TranslationAgent()
        
        # 检查基本属性
        assert hasattr(agent, 'client')
        assert hasattr(agent, 'model')
        
        print("✅ 翻译智能体正常")
        return True
    except Exception as e:
        print(f"❌ 翻译智能体错误: {e}")
        return False

def test_review_agent():
    """测试校对智能体"""
    print("🧪 测试校对智能体...")
    try:
        from agents.review_agent import ReviewAgent
        agent = ReviewAgent()
        
        # 检查基本属性
        assert hasattr(agent, 'client')
        assert hasattr(agent, 'model')
        
        print("✅ 校对智能体正常")
        return True
    except Exception as e:
        print(f"❌ 校对智能体错误: {e}")
        return False

def test_translation_service():
    """测试翻译服务"""
    print("🧪 测试翻译服务...")
    try:
        from services.translation_service import TranslationService
        service = TranslationService()
        
        # 检查基本属性
        assert hasattr(service, 'doc_processor')
        assert hasattr(service, 'knowledge_base')
        assert hasattr(service, 'translation_agent')
        assert hasattr(service, 'review_agent')
        
        print("✅ 翻译服务正常")
        return True
    except Exception as e:
        print(f"❌ 翻译服务错误: {e}")
        return False

def test_gradio_interface():
    """测试Gradio界面"""
    print("🧪 测试Gradio界面...")
    try:
        from app import create_interface
        app = create_interface()
        
        # 检查界面创建成功
        assert app is not None
        
        print("✅ Gradio界面正常")
        return True
    except Exception as e:
        print(f"❌ Gradio界面错误: {e}")
        return False

def test_chrome_driver():
    """测试Chrome WebDriver"""
    print("🧪 测试Chrome WebDriver...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        assert "Google" in title
        print("✅ Chrome WebDriver正常")
        return True
    except Exception as e:
        print(f"⚠️  Chrome WebDriver不可用: {e}")
        print("   网页爬虫功能将受限，但不影响翻译功能")
        return False

def create_test_document():
    """创建测试文档"""
    print("🧪 创建测试文档...")
    try:
        from docx import Document
        
        # 创建测试文档
        doc = Document()
        doc.add_heading('测试公司年报', 0)
        doc.add_paragraph('公司名称：测试有限公司')
        doc.add_paragraph('股票代码：0001')
        doc.add_paragraph('报告类型：年报')
        doc.add_paragraph('报告年份：2023')
        doc.add_paragraph('这是一个测试文档，用于验证翻译系统的功能。')
        
        # 添加表格
        table = doc.add_table(rows=2, cols=2)
        table.cell(0, 0).text = '项目'
        table.cell(0, 1).text = '金额'
        table.cell(1, 0).text = '营业收入'
        table.cell(1, 1).text = '1,000,000'
        
        # 保存测试文档
        test_file = Path("test_document.docx")
        doc.save(test_file)
        
        print(f"✅ 测试文档已创建: {test_file}")
        return str(test_file)
    except Exception as e:
        print(f"❌ 创建测试文档失败: {e}")
        return None

def run_integration_test():
    """运行集成测试"""
    print("🧪 运行集成测试...")
    try:
        # 创建测试文档
        test_file = create_test_document()
        if not test_file:
            return False
        
        # 测试文档预览
        from services.translation_service import TranslationService
        service = TranslationService()
        
        preview = service.get_translation_preview(test_file)
        assert preview.get('success', False)
        
        print("✅ 集成测试通过")
        
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)
            print("🧹 测试文件已清理")
        
        return True
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始系统测试...")
    print("=" * 60)
    
    tests = [
        ("配置模块", test_config),
        ("文档处理模块", test_document_processor),
        ("知识库模块", test_knowledge_base),
        ("成本估算模块", test_cost_estimator),
        ("翻译历史模块", test_translation_history),
        ("翻译智能体", test_translation_agent),
        ("校对智能体", test_review_agent),
        ("翻译服务", test_translation_service),
        ("Gradio界面", test_gradio_interface),
        ("Chrome WebDriver", test_chrome_driver),
        ("集成测试", run_integration_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常使用。")
        return True
    elif passed >= total - 1:  # 允许Chrome WebDriver失败
        print("✅ 核心功能测试通过！系统可以正常使用。")
        print("⚠️  部分非核心功能可能受限。")
        return True
    else:
        print("❌ 系统存在问题，请检查配置和依赖。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
