#!/usr/bin/env python3
"""
分析真实的港交所文档
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_document():
    """分析真实文档"""
    try:
        from docx import Document
        from utils.document_processor import DocumentProcessor
        
        doc_path = "2022_ar_e.docx"
        
        print("📄 分析真实港交所文档...")
        print("=" * 60)
        
        # 1. 基本文档信息
        doc = Document(doc_path)
        print(f"📊 基本信息:")
        print(f"   段落数量: {len(doc.paragraphs)}")
        print(f"   表格数量: {len(doc.tables)}")
        
        # 2. 显示前20段内容
        print(f"\n📖 前20段内容:")
        for i, paragraph in enumerate(doc.paragraphs[:20]):
            text = paragraph.text.strip()
            if text:
                print(f"   段落 {i+1}: {text[:100]}{'...' if len(text) > 100 else ''}")
        
        # 3. 显示表格内容
        print(f"\n📋 表格内容 (前3个表格):")
        for table_idx, table in enumerate(doc.tables[:3]):
            print(f"   表格 {table_idx + 1}:")
            for row_idx, row in enumerate(table.rows[:3]):  # 只显示前3行
                row_text = " | ".join([cell.text.strip() for cell in row.cells])
                if row_text.strip():
                    print(f"     行 {row_idx + 1}: {row_text[:150]}{'...' if len(row_text) > 150 else ''}")
        
        # 4. 使用文档处理器提取信息
        print(f"\n🔍 使用文档处理器提取信息:")
        processor = DocumentProcessor()
        company_info = processor.extract_company_info(doc_path)
        
        print(f"   提取结果:")
        for key, value in company_info.items():
            print(f"     {key}: '{value}'")
        
        # 5. 搜索关键信息
        print(f"\n🔎 搜索关键信息模式:")
        
        # 合并所有文本内容
        all_text = ""
        for paragraph in doc.paragraphs:
            all_text += paragraph.text + "\n"
        
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    all_text += cell.text + "\n"
        
        # 搜索股票代码相关
        import re
        
        print(f"   搜索股票代码模式:")
        stock_patterns = [
            r"股票代码[：:\s]*(\d{4,5})",
            r"Stock Code[：:\s]*(\d{4,5})",
            r"代码[：:\s]*(\d{4,5})",
            r"(\d{4})\.HK",
            r"HK(\d{4})",
        ]
        
        for pattern in stock_patterns:
            matches = re.findall(pattern, all_text, re.IGNORECASE)
            if matches:
                print(f"     模式 '{pattern}': {matches[:5]}")  # 只显示前5个匹配
        
        # 搜索公司名称
        print(f"   搜索公司名称模式:")
        company_patterns = [
            r"([^，。\n]{5,50}(?:有限公司|Limited|Ltd|Corporation|Holdings|Group))",
        ]
        
        for pattern in company_patterns:
            matches = re.findall(pattern, all_text)
            if matches:
                unique_matches = list(set(matches))[:5]  # 去重并只显示前5个
                print(f"     找到公司名称: {unique_matches}")
        
        # 搜索年份
        print(f"   搜索年份模式:")
        year_patterns = [
            r"(\d{4})年",
            r"Year (\d{4})",
            r"20\d{2}",
        ]
        
        for pattern in year_patterns:
            matches = re.findall(pattern, all_text)
            if matches:
                unique_years = sorted(list(set(matches)))
                print(f"     模式 '{pattern}': {unique_years}")
        
        # 6. 检查文档属性
        print(f"\n📋 文档属性:")
        try:
            core_props = doc.core_properties
            print(f"   标题: {core_props.title}")
            print(f"   作者: {core_props.author}")
            print(f"   主题: {core_props.subject}")
            print(f"   创建时间: {core_props.created}")
        except Exception as e:
            print(f"   无法读取文档属性: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    analyze_document()
