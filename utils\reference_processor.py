"""
参考文档处理模块 - 处理用户提供的中英文参考文档对
"""
import os
import re
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from docx import Document
import logging

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)

class ReferenceProcessor:
    """参考文档处理器"""
    
    def __init__(self):
        self.supported_formats = ['.docx']
    
    def extract_basic_info(self, doc_path: str) -> Dict[str, str]:
        """
        从文档中提取基本信息
        
        Args:
            doc_path: 文档路径
            
        Returns:
            包含基本信息的字典
        """
        try:
            doc = Document(doc_path)
            basic_info = {
                "file_name": os.path.basename(doc_path),
                "total_paragraphs": len(doc.paragraphs),
                "total_tables": len(doc.tables),
                "language": "unknown",
                "content_preview": ""
            }

            # 收集前几段内容用于语言检测和预览
            content_parts = []
            for paragraph in doc.paragraphs[:20]:
                text = paragraph.text.strip()
                if text:
                    content_parts.append(text)

            if content_parts:
                preview_text = " ".join(content_parts)
                basic_info["content_preview"] = preview_text[:500]
                
                # 简单的语言检测
                chinese_chars = sum(1 for char in preview_text if '\u4e00' <= char <= '\u9fff')
                if chinese_chars > len(preview_text) * 0.1:
                    basic_info["language"] = "zh"
                else:
                    basic_info["language"] = "en"

            logger.info(f"提取的基本信息: {basic_info}")
            return basic_info

        except Exception as e:
            logger.error(f"提取基本信息失败: {e}")
            return {}
    
    def split_document_by_pages(self, doc_path: str, pages_per_chunk: int = 1) -> List[Dict]:
        """
        按页分割文档
        
        Args:
            doc_path: 文档路径
            pages_per_chunk: 每个块的页数
            
        Returns:
            分页后的文档块列表
        """
        try:
            doc = Document(doc_path)
            pages = []
            current_page = {
                'page_number': 1,
                'content': '',
                'paragraphs': [],
                'tables': [],
                'images': []
            }
            
            paragraph_count = 0
            
            for paragraph in doc.paragraphs:
                # 检查是否为分页符
                if self._is_page_break(paragraph):
                    if current_page['content'].strip():
                        pages.append(current_page)
                    current_page = {
                        'page_number': len(pages) + 1,
                        'content': '',
                        'paragraphs': [],
                        'tables': [],
                        'images': []
                    }
                    paragraph_count = 0
                    continue
                
                # 添加段落内容
                if paragraph.text.strip():
                    current_page['content'] += paragraph.text + '\n'
                    current_page['paragraphs'].append({
                        'text': paragraph.text,
                        'style': paragraph.style.name if paragraph.style else 'Normal',
                        'alignment': paragraph.alignment
                    })
                    paragraph_count += 1
                
                # 简单的分页逻辑：每30个段落为一页
                if paragraph_count >= 30:
                    pages.append(current_page)
                    current_page = {
                        'page_number': len(pages) + 1,
                        'content': '',
                        'paragraphs': [],
                        'tables': [],
                        'images': []
                    }
                    paragraph_count = 0
            
            # 添加最后一页
            if current_page['content'].strip():
                pages.append(current_page)
            
            # 处理表格
            for table in doc.tables:
                # 简单地将表格分配给第一页（实际应用中需要更复杂的逻辑）
                if pages:
                    table_data = []
                    for row in table.rows:
                        row_data = [cell.text for cell in row.cells]
                        table_data.append(row_data)
                    pages[0]['tables'].append(table_data)
            
            logger.info(f"文档分割完成，共 {len(pages)} 页")
            return pages
            
        except Exception as e:
            logger.error(f"文档分割失败: {e}")
            return []
    
    def _is_page_break(self, paragraph) -> bool:
        """检查段落是否包含分页符"""
        try:
            for run in paragraph.runs:
                if '\f' in run.text or '\x0c' in run.text:
                    return True
            return False
        except:
            return False
    
    def extract_bilingual_pairs(self, zh_doc_path: str, en_doc_path: str) -> List[Dict]:
        """
        从中英文文档对中提取双语对照内容
        
        Args:
            zh_doc_path: 中文文档路径
            en_doc_path: 英文文档路径
            
        Returns:
            双语对照内容列表
        """
        try:
            # 分别分割中英文文档
            zh_pages = self.split_document_by_pages(zh_doc_path)
            en_pages = self.split_document_by_pages(en_doc_path)
            
            bilingual_pairs = []
            
            # 按页面对应关系建立双语对照
            max_pages = max(len(zh_pages), len(en_pages))
            
            for i in range(max_pages):
                zh_content = zh_pages[i]['content'] if i < len(zh_pages) else ""
                en_content = en_pages[i]['content'] if i < len(en_pages) else ""
                
                if zh_content.strip() or en_content.strip():
                    bilingual_pairs.append({
                        'page_number': i + 1,
                        'zh_content': zh_content.strip(),
                        'en_content': en_content.strip(),
                        'zh_paragraphs': zh_pages[i]['paragraphs'] if i < len(zh_pages) else [],
                        'en_paragraphs': en_pages[i]['paragraphs'] if i < len(en_pages) else []
                    })
            
            logger.info(f"提取了 {len(bilingual_pairs)} 个双语对照页面")
            return bilingual_pairs
            
        except Exception as e:
            logger.error(f"提取双语对照失败: {e}")
            return []
    
    def extract_terminology_pairs(self, bilingual_pairs: List[Dict]) -> List[Dict]:
        """
        从双语对照中提取术语对照
        
        Args:
            bilingual_pairs: 双语对照内容列表
            
        Returns:
            术语对照列表
        """
        try:
            terminology_pairs = []
            
            # 常见的财务和法律术语模式
            zh_term_patterns = [
                r'([^，。\n]{2,20}(?:收入|支出|利润|亏损|资产|负债|股本|股份|董事|监事|审计|合规|风险|投资|融资|并购|重组))',
                r'([^，。\n]{2,15}(?:有限公司|股份有限公司|集团|控股|科技|投资|发展|实业|国际))',
                r'([^，。\n]{2,10}(?:报告|声明|公告|通知|决议|协议|合同|章程|制度|政策))',
            ]
            
            en_term_patterns = [
                r'([A-Z][a-z\s]{2,30}(?:Revenue|Income|Profit|Loss|Assets|Liabilities|Equity|Shares|Director|Auditor|Compliance|Risk|Investment|Financing|Acquisition|Restructuring))',
                r'([A-Z][A-Za-z\s&]{5,40}(?:Limited|Corporation|Holdings|Group|Technology|Investment|Development|International))',
                r'([A-Z][a-z\s]{2,20}(?:Report|Statement|Announcement|Notice|Resolution|Agreement|Contract|Charter|Policy))',
            ]
            
            for pair in bilingual_pairs:
                zh_content = pair['zh_content']
                en_content = pair['en_content']
                
                # 提取中文术语
                zh_terms = []
                for pattern in zh_term_patterns:
                    matches = re.findall(pattern, zh_content)
                    zh_terms.extend(matches)
                
                # 提取英文术语
                en_terms = []
                for pattern in en_term_patterns:
                    matches = re.findall(pattern, en_content)
                    en_terms.extend(matches)
                
                # 建立术语对照（简单的位置对应）
                for i, zh_term in enumerate(zh_terms):
                    if i < len(en_terms):
                        terminology_pairs.append({
                            'zh_term': zh_term.strip(),
                            'en_term': en_terms[i].strip(),
                            'page_number': pair['page_number'],
                            'confidence': 0.7  # 简单的置信度
                        })
            
            # 去重
            seen = set()
            unique_pairs = []
            for pair in terminology_pairs:
                key = (pair['zh_term'], pair['en_term'])
                if key not in seen:
                    seen.add(key)
                    unique_pairs.append(pair)
            
            logger.info(f"提取了 {len(unique_pairs)} 个术语对照")
            return unique_pairs
            
        except Exception as e:
            logger.error(f"提取术语对照失败: {e}")
            return []
    
    def create_translated_document(self, original_doc_path: str, translated_pages: List[Dict], 
                                 output_path: str) -> str:
        """
        创建翻译后的文档，保持原有格式
        
        Args:
            original_doc_path: 原始文档路径
            translated_pages: 翻译后的页面列表
            output_path: 输出路径
            
        Returns:
            输出文档路径
        """
        try:
            # 创建新文档
            new_doc = Document()
            
            # 复制原文档的样式设置
            original_doc = Document(original_doc_path)
            
            for page in translated_pages:
                # 添加页面内容
                for para_info in page.get('paragraphs', []):
                    p = new_doc.add_paragraph()
                    p.text = para_info.get('translated_text', para_info.get('text', ''))
                    
                    # 尝试保持原有样式
                    try:
                        if para_info.get('style'):
                            p.style = para_info['style']
                        if para_info.get('alignment'):
                            p.alignment = para_info['alignment']
                    except:
                        pass  # 如果样式设置失败，使用默认样式
                
                # 添加表格
                for table_data in page.get('tables', []):
                    if table_data:
                        table = new_doc.add_table(rows=len(table_data), cols=len(table_data[0]))
                        for i, row_data in enumerate(table_data):
                            for j, cell_text in enumerate(row_data):
                                table.cell(i, j).text = cell_text
                
                # 添加分页符（除了最后一页）
                if page != translated_pages[-1]:
                    new_doc.add_page_break()
            
            # 保存文档
            new_doc.save(output_path)
            logger.info(f"翻译文档已保存: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"创建翻译文档失败: {e}")
            raise
    
    def validate_document(self, doc_path: str) -> Tuple[bool, str]:
        """
        验证文档是否有效
        
        Args:
            doc_path: 文档路径
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            if not os.path.exists(doc_path):
                return False, "文件不存在"
            
            file_size = os.path.getsize(doc_path) / (1024 * 1024)  # MB
            if file_size > 50:  # 50MB限制
                return False, f"文件过大: {file_size:.1f}MB，最大支持50MB"
            
            # 尝试打开文档
            doc = Document(doc_path)
            
            # 检查是否有内容
            if len(doc.paragraphs) == 0:
                return False, "文档为空"
            
            return True, ""
            
        except Exception as e:
            return False, f"文档格式错误: {str(e)}"
