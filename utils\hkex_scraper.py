"""
HKEx网站爬虫模块 - 获取上市公司披露报告
"""
import os
import re
import time
import requests
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from config import Config

# 确保utils目录存在
os.makedirs(os.path.dirname(__file__), exist_ok=True)

logger = logging.getLogger(__name__)

class HKExScraper:
    """港交所网站爬虫"""
    
    def __init__(self):
        self.base_url = Config.HKEX_BASE_URL
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.driver = None
    
    def _setup_driver(self):
        """设置Selenium WebDriver"""
        if self.driver is None:
            chrome_options = Options()
            if Config.HEADLESS_BROWSER:
                chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            
            try:
                self.driver = webdriver.Chrome(options=chrome_options)
                logger.info("Chrome WebDriver 初始化成功")
            except Exception as e:
                logger.error(f"WebDriver 初始化失败: {e}")
                raise
    
    def search_company_reports(self, company_info: Dict[str, str]) -> List[Dict[str, str]]:
        """
        搜索公司报告
        
        Args:
            company_info: 公司信息字典，包含股票代码、公司名称等
            
        Returns:
            报告列表
        """
        try:
            self._setup_driver()
            
            # 构建搜索URL
            search_url = f"{self.base_url}/search/titlesearch.xhtml"
            
            self.driver.get(search_url)
            time.sleep(2)
            
            # 输入搜索条件
            stock_code = company_info.get('stock_code', '')
            company_name = company_info.get('company_name_zh', '') or company_info.get('company_name_en', '')
            
            # 尝试通过股票代码搜索
            if stock_code:
                search_results = self._search_by_stock_code(stock_code)
                if search_results:
                    return self._filter_reports_by_type(search_results, company_info.get('report_type', ''))
            
            # 如果股票代码搜索失败，尝试公司名称搜索
            if company_name:
                search_results = self._search_by_company_name(company_name)
                if search_results:
                    return self._filter_reports_by_type(search_results, company_info.get('report_type', ''))
            
            logger.warning(f"未找到公司 {company_name} ({stock_code}) 的报告")
            return []
            
        except Exception as e:
            logger.error(f"搜索公司报告失败: {e}")
            return []
        finally:
            if self.driver:
                self.driver.quit()
                self.driver = None
    
    def _search_by_stock_code(self, stock_code: str) -> List[Dict[str, str]]:
        """通过股票代码搜索"""
        try:
            # 查找搜索框并输入股票代码
            search_box = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "searchText"))
            )
            search_box.clear()
            search_box.send_keys(stock_code)
            
            # 点击搜索按钮
            search_button = self.driver.find_element(By.XPATH, "//input[@type='submit' and @value='Search']")
            search_button.click()
            
            # 等待结果加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "search-result"))
            )
            
            return self._parse_search_results()
            
        except (TimeoutException, NoSuchElementException) as e:
            logger.warning(f"股票代码搜索失败: {e}")
            return []
    
    def _search_by_company_name(self, company_name: str) -> List[Dict[str, str]]:
        """通过公司名称搜索"""
        try:
            # 清空搜索框并输入公司名称
            search_box = self.driver.find_element(By.NAME, "searchText")
            search_box.clear()
            search_box.send_keys(company_name)
            
            # 点击搜索按钮
            search_button = self.driver.find_element(By.XPATH, "//input[@type='submit' and @value='Search']")
            search_button.click()
            
            # 等待结果加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CLASS_NAME, "search-result"))
            )
            
            return self._parse_search_results()
            
        except (TimeoutException, NoSuchElementException) as e:
            logger.warning(f"公司名称搜索失败: {e}")
            return []
    
    def _parse_search_results(self) -> List[Dict[str, str]]:
        """解析搜索结果"""
        try:
            results = []
            
            # 查找所有搜索结果项
            result_items = self.driver.find_elements(By.CLASS_NAME, "search-result-item")
            
            for item in result_items:
                try:
                    # 提取标题和链接
                    title_element = item.find_element(By.TAG_NAME, "a")
                    title = title_element.text.strip()
                    link = title_element.get_attribute("href")
                    
                    # 提取日期
                    date_element = item.find_element(By.CLASS_NAME, "date")
                    date = date_element.text.strip() if date_element else ""
                    
                    # 提取文件类型
                    file_type = "PDF" if "pdf" in link.lower() else "HTML"
                    
                    results.append({
                        'title': title,
                        'link': link,
                        'date': date,
                        'file_type': file_type,
                        'language': self._detect_language(title)
                    })
                    
                except Exception as e:
                    logger.warning(f"解析搜索结果项失败: {e}")
                    continue
            
            logger.info(f"找到 {len(results)} 个搜索结果")
            return results
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {e}")
            return []
    
    def _detect_language(self, text: str) -> str:
        """检测文本语言"""
        # 简单的语言检测
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        if chinese_chars > len(text) * 0.3:
            return 'zh'
        return 'en'
    
    def _filter_reports_by_type(self, reports: List[Dict[str, str]], report_type: str) -> List[Dict[str, str]]:
        """根据报告类型过滤报告"""
        if not report_type:
            return reports
        
        type_keywords = {
            'annual_report': ['annual report', '年报', '年度报告'],
            'interim_report': ['interim report', '中期报告', '半年报'],
            'quarterly_report': ['quarterly report', '季度报告', '季报']
        }
        
        keywords = type_keywords.get(report_type, [])
        if not keywords:
            return reports
        
        filtered_reports = []
        for report in reports:
            title_lower = report['title'].lower()
            if any(keyword.lower() in title_lower for keyword in keywords):
                filtered_reports.append(report)
        
        return filtered_reports
    
    def download_report(self, report_url: str, save_path: str) -> bool:
        """
        下载报告文件
        
        Args:
            report_url: 报告URL
            save_path: 保存路径
            
        Returns:
            是否下载成功
        """
        try:
            response = self.session.get(report_url, stream=True, timeout=30)
            response.raise_for_status()
            
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            logger.info(f"报告下载成功: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"下载报告失败: {e}")
            return False
    
    def get_latest_bilingual_reports(self, company_info: Dict[str, str]) -> Tuple[Optional[str], Optional[str]]:
        """
        获取最新的双语报告
        
        Args:
            company_info: 公司信息
            
        Returns:
            (中文报告路径, 英文报告路径)
        """
        try:
            reports = self.search_company_reports(company_info)
            if not reports:
                return None, None
            
            # 按日期排序，获取最新的报告
            reports.sort(key=lambda x: x.get('date', ''), reverse=True)
            
            zh_report = None
            en_report = None
            
            # 查找中英文版本
            for report in reports:
                if report['language'] == 'zh' and not zh_report:
                    zh_report = report
                elif report['language'] == 'en' and not en_report:
                    en_report = report
                
                # 如果找到了双语版本，退出循环
                if zh_report and en_report:
                    break
            
            # 下载报告
            zh_path = None
            en_path = None
            
            if zh_report:
                zh_filename = f"zh_{company_info.get('stock_code', 'unknown')}_{zh_report.get('date', '')}.pdf"
                zh_path = os.path.join(Config.KNOWLEDGE_BASE_DIR, zh_filename)
                if self.download_report(zh_report['link'], zh_path):
                    logger.info(f"中文报告下载成功: {zh_path}")
                else:
                    zh_path = None
            
            if en_report:
                en_filename = f"en_{company_info.get('stock_code', 'unknown')}_{en_report.get('date', '')}.pdf"
                en_path = os.path.join(Config.KNOWLEDGE_BASE_DIR, en_filename)
                if self.download_report(en_report['link'], en_path):
                    logger.info(f"英文报告下载成功: {en_path}")
                else:
                    en_path = None
            
            return zh_path, en_path
            
        except Exception as e:
            logger.error(f"获取双语报告失败: {e}")
            return None, None
    
    def __del__(self):
        """清理资源"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
