# 使用指南

## 🚀 快速开始

### 方法一：自动安装（推荐）

```bash
# 1. 运行自动安装脚本
python install.py

# 2. 按提示配置API密钥

# 3. 启动系统
python start.py
```

### 方法二：手动安装

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境
cp .env.example .env
# 编辑.env文件，设置OPENROUTER_API_KEY

# 3. 测试系统
python test_system.py

# 4. 启动系统
python app.py
```

## 📋 系统要求

- **Python**: 3.8+
- **内存**: 建议4GB+
- **存储**: 建议2GB+可用空间
- **网络**: 稳定的互联网连接
- **浏览器**: Chrome（可选，用于网页爬虫）

## 🔑 API密钥配置

### 获取OpenRouter API密钥

1. 访问 [OpenRouter](https://openrouter.ai/)
2. 注册账号并登录
3. 在控制台获取API密钥
4. 在.env文件中设置：
   ```
   OPENROUTER_API_KEY=your_actual_api_key_here
   ```

### 推荐模型配置

```bash
# 高质量翻译（成本较高）
DEFAULT_TRANSLATION_MODEL=anthropic/claude-3.5-sonnet
DEFAULT_REVIEW_MODEL=anthropic/claude-3.5-sonnet

# 经济型翻译（成本较低）
DEFAULT_TRANSLATION_MODEL=anthropic/claude-3-haiku
DEFAULT_REVIEW_MODEL=anthropic/claude-3-haiku
```

## 📖 详细使用步骤

### 1. 单文档翻译

#### 步骤1：上传文档
- 点击"上传DOCX文件"
- 选择要翻译的披露报告
- 支持格式：.docx
- 最大文件：50MB

#### 步骤2：预览文档
- 点击"📋 预览文档"
- 查看文件信息、公司信息
- 检查成本估算
- 确认翻译方向

#### 步骤3：开始翻译
- 选择翻译方向：
  - 中文→英文
  - 英文→中文
- 点击"🚀 开始翻译"
- 等待翻译完成

#### 步骤4：下载结果
- 查看翻译质量报告
- 下载翻译后的文档
- 保存项目ID以便后续查询

### 2. 批量翻译

#### 步骤1：上传多文件
- 选择"📦 批量翻译"标签页
- 上传多个DOCX文件
- 设置统一的翻译方向

#### 步骤2：开始批量处理
- 点击"🚀 开始批量翻译"
- 系统将依次处理所有文件
- 查看处理进度

#### 步骤3：查看结果
- 查看批量处理报告
- 下载成功翻译的文件
- 检查失败文件的错误信息

### 3. 翻译历史管理

#### 查看历史记录
- 选择"📚 翻译历史"标签页
- 查看最近的翻译项目
- 点击"🔄 刷新历史"更新

#### 成本统计
- 查看最近30天的使用统计
- 监控翻译成本和趋势
- 优化使用策略

## ⚙️ 高级配置

### 成本控制

```bash
# 单文档最大成本（美元）
MAX_COST_PER_DOCUMENT=50.0

# 成本警告阈值（美元）
COST_WARNING_THRESHOLD=30.0
```

### 文件处理限制

```bash
# 最大文件大小（MB）
MAX_FILE_SIZE_MB=50

# 最大页数
MAX_PAGES_PER_DOCUMENT=200

# 批量处理文件数
BATCH_SIZE=5
```

### 翻译质量控制

```bash
# 最大修订次数
MAX_RETRY_ATTEMPTS=5

# 翻译超时时间（秒）
TRANSLATION_TIMEOUT=300
```

## 🎯 最佳实践

### 1. 首次使用建议

- **建立知识库**：先翻译几个同类型报告
- **测试小文档**：从较小的文档开始测试
- **检查成本**：关注成本估算，避免超预算
- **验证质量**：人工抽查翻译结果

### 2. 提高翻译质量

- **同公司报告**：优先处理同公司的历史报告
- **术语一致性**：确保专业术语翻译一致
- **格式检查**：验证输出文档格式完整性
- **人工校对**：重要文档建议人工最终校对

### 3. 成本优化

- **选择合适模型**：根据质量要求选择模型
- **批量处理**：相关文档一起处理更经济
- **预览功能**：使用预览避免不必要的翻译
- **监控使用量**：定期查看成本统计

### 4. 故障处理

- **网络问题**：确保网络连接稳定
- **内存不足**：减少批量处理文件数
- **API限制**：检查API密钥和余额
- **文档格式**：确保输入文件格式正确

## 🔧 故障排除

### 常见问题

#### 1. 启动失败
```bash
# 检查Python版本
python --version

# 重新安装依赖
pip install -r requirements.txt

# 运行测试
python test_system.py
```

#### 2. API错误
```bash
# 检查API密钥
cat .env | grep OPENROUTER_API_KEY

# 测试API连接
curl -H "Authorization: Bearer YOUR_API_KEY" https://openrouter.ai/api/v1/models
```

#### 3. 翻译质量问题
- 检查知识库是否有相关参考资料
- 尝试使用更高质量的模型
- 增加修订次数限制
- 人工校对关键术语

#### 4. 成本过高
- 使用经济型模型（claude-3-haiku）
- 减少文档页数
- 优化文档内容
- 分批处理大文档

### 日志查看

```bash
# 查看应用日志
tail -f translation_app.log

# 查看错误信息
grep ERROR translation_app.log
```

## 📞 技术支持

### 获取帮助

1. **查看日志**：检查translation_app.log文件
2. **运行测试**：执行python test_system.py
3. **检查配置**：验证.env文件设置
4. **重启系统**：重新启动应用

### 性能优化

- **内存优化**：关闭不必要的程序
- **网络优化**：使用稳定的网络连接
- **存储优化**：定期清理临时文件
- **模型选择**：根据需求选择合适的AI模型

### 数据备份

```bash
# 备份翻译历史
cp -r translation_history/ backup/

# 备份知识库
cp -r chroma_db/ backup/

# 备份配置
cp .env backup/
```

---

💡 **提示**：如遇到问题，请先查看日志文件，大多数问题都有详细的错误信息。
